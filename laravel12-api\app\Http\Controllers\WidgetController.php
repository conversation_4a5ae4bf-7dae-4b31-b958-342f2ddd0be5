<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreWidgetRequest;
use App\Http\Requests\UpdateWidgetRequest;
use App\Models\Widget;
use App\Services\WidgetService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class WidgetController extends Controller
{
    protected WidgetService $widgetService;

    public function __construct(WidgetService $widgetService)
    {
        $this->widgetService = $widgetService;
    }

    /**
     * Display a listing of the widgets.
     */
    public function index(Request $request): JsonResponse
    {
        $widgets = $this->widgetService->getUserWidgets($request->user()->id);

        return response()->json([
            'status' => 'success',
            'data' => $widgets
        ]);
    }

    /**
     * Store a newly created widget.
     */
    public function store(StoreWidgetRequest $request): JsonResponse
    {
        $widget = $this->widgetService->createWidget($request->validated(), $request->user()->id);

        return response()->json([
            'status' => 'success',
            'message' => 'Widget created successfully',
            'data' => $widget
        ], 201);
    }

    /**
     * Display the specified widget.
     */
    public function show(string $id): JsonResponse
    {
        $widget = $this->widgetService->getWidget($id);

        return response()->json([
            'status' => 'success',
            'data' => $widget
        ]);
    }

    /**
     * Update the specified widget.
     */
    public function update(UpdateWidgetRequest $request, string $id): JsonResponse
    {
        $widget = $this->widgetService->updateWidget($id, $request->validated());

        return response()->json([
            'status' => 'success',
            'message' => 'Widget updated successfully',
            'data' => $widget
        ]);
    }

    /**
     * Remove the specified widget.
     */
    public function destroy(string $id): JsonResponse
    {
        $this->widgetService->deleteWidget($id);

        return response()->json([
            'status' => 'success',
            'message' => 'Widget deleted successfully'
        ]);
    }

    /**
     * Increment widget view count.
     */
    public function trackView(string $slug): JsonResponse
    {
        $this->widgetService->incrementViews($slug);

        return response()->json([
            'status' => 'success'
        ]);
    }

    /**
     * Increment widget interaction count.
     */
    public function trackInteraction(string $slug): JsonResponse
    {
        $this->widgetService->incrementInteractions($slug);

        return response()->json([
            'status' => 'success'
        ]);
    }

    /**
     * Get public widget by slug.
     */
    public function getPublicWidget(string $slug): JsonResponse
    {
        $widget = $this->widgetService->getWidgetBySlug($slug);

        return response()->json([
            'status' => 'success',
            'data' => $widget
        ]);
    }
}
