"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Monitor, Smartphone, MessageCircle, Sparkles } from "lucide-react"
import { SharedWidgetPreview } from "@/components/widget/shared-widget-preview"

interface EmbedCodePreviewProps {
    widgetId: string
    format: string
}

export function EmbedCodePreview({ widgetId, format }: EmbedCodePreviewProps) {
    const [previewMode, setPreviewMode] = useState("desktop")
    const [widgetOpen, setWidgetOpen] = useState(true)

    const MockWebsite = ({ isMobile = false }: { isMobile?: boolean }) => (
        <div className={`bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 rounded-xl ${isMobile ? "h-[650px]" : "h-[550px]"
            } relative overflow-visible shadow-inner`}>
            {/* Mock browser/mobile frame */}
            {!isMobile ? (
                <div className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl border border-slate-200 dark:border-slate-700 h-full flex flex-col backdrop-blur-sm">
                    <div className="flex items-center gap-3 p-4 border-b border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800 rounded-t-xl flex-shrink-0">
                        <div className="flex gap-2">
                            <div className="w-3 h-3 rounded-full bg-red-500 shadow-sm"></div>
                            <div className="w-3 h-3 rounded-full bg-yellow-500 shadow-sm"></div>
                            <div className="w-3 h-3 rounded-full bg-green-500 shadow-sm"></div>
                        </div>
                        <div className="flex-1 text-center">
                            <div className="bg-white dark:bg-slate-700 rounded-lg px-4 py-1.5 text-sm text-slate-600 dark:text-slate-300 border border-slate-200 dark:border-slate-600">
                                🔒 yourwebsite.com
                            </div>
                        </div>
                    </div>
                    <div className="flex-1 p-8 flex items-center justify-center bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
                        <div className="text-center">
                            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-4 mx-auto shadow-lg">
                                <Sparkles className="h-8 w-8 text-white" />
                            </div>
                            <h3 className="text-xl font-semibold text-slate-800 dark:text-slate-200 mb-2">Your Website</h3>
                            <p className="text-slate-500 dark:text-slate-400">
                                The AI chat widget will appear here
                            </p>
                        </div>
                    </div>
                </div>
            ) : (
                <div className="w-full h-full bg-black rounded-[2.5rem] p-2 shadow-2xl">
                    <div className="w-full h-full bg-white dark:bg-slate-900 rounded-[2rem] overflow-hidden flex flex-col">
                        <div className="bg-slate-100 dark:bg-slate-800 px-6 py-3 flex justify-between items-center text-sm flex-shrink-0">
                            <span className="font-semibold text-slate-800 dark:text-slate-200">9:41</span>
                            <div className="flex items-center gap-2">
                                <div className="flex gap-1">
                                    <div className="w-1 h-1 bg-slate-800 dark:bg-slate-200 rounded-full"></div>
                                    <div className="w-1 h-1 bg-slate-800 dark:bg-slate-200 rounded-full"></div>
                                    <div className="w-1 h-1 bg-slate-800 dark:bg-slate-200 rounded-full"></div>
                                </div>
                                <div className="w-6 h-3 border-2 border-slate-800 dark:border-slate-200 rounded-sm">
                                    <div className="w-4 h-1.5 bg-green-500 rounded-sm m-0.5"></div>
                                </div>
                            </div>
                        </div>
                        <div className="flex-1 p-6 flex items-center justify-center bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
                            <div className="text-center">
                                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-3 mx-auto shadow-lg">
                                    <Sparkles className="h-6 w-6 text-white" />
                                </div>
                                <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-2">Mobile Website</h3>
                                <p className="text-sm text-slate-500 dark:text-slate-400">
                                    Widget preview
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Premium Chat Widget */}
            {widgetOpen && (
                <div className={`absolute ${isMobile ? "bottom-3 right-3" : "bottom-4 right-4"
                    } z-20 transform transition-all duration-300 hover:scale-105`}>
                    <SharedWidgetPreview
                        isMobile={isMobile}
                        onClose={() => setWidgetOpen(false)}
                        showCloseButton={true}
                    />
                </div>
            )}

            {/* Premium Chat Button (when widget is closed) */}
            {!widgetOpen && (
                <div className={`absolute ${isMobile ? "bottom-4 right-4" : "bottom-4 right-6"
                    } z-20`}>
                    <Button
                        className="rounded-full w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-2xl border-4 border-white dark:border-slate-800 transform transition-all duration-300 hover:scale-110 group"
                        onClick={() => setWidgetOpen(true)}
                    >
                        <MessageCircle className="h-7 w-7 text-white group-hover:scale-110 transition-transform" />
                        <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                            <span className="text-xs font-bold text-white">3</span>
                        </div>
                    </Button>
                </div>
            )}
        </div>
    )

    return (
        <div className="space-y-6">
            {/* Preview Controls */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                    <Badge variant="secondary" className="text-xs font-medium px-3 py-1">
                        {format.toUpperCase()} Preview
                    </Badge>
                    <span className="text-sm text-slate-600 dark:text-slate-400">
                        Widget ID: <code className="bg-slate-100 dark:bg-slate-800 px-2 py-1 rounded text-xs">{widgetId}</code>
                    </span>
                </div>
                <div className="bg-white dark:bg-slate-900 rounded-2xl shadow-lg border border-slate-200 dark:border-slate-700 p-1">
                    <Tabs value={previewMode} onValueChange={setPreviewMode} className="w-auto">
                        <TabsList className="grid w-full grid-cols-2 bg-transparent gap-1 h-auto p-1">
                            <TabsTrigger
                                value="desktop"
                                className="flex items-center justify-center gap-2 rounded-lg py-2.5 px-3 text-sm font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white data-[state=active]:hover:from-blue-600 data-[state=active]:hover:to-purple-600"
                            >
                                <Monitor className="h-4 w-4" />
                                <span>Desktop</span>
                            </TabsTrigger>
                            <TabsTrigger
                                value="mobile"
                                className="flex items-center justify-center gap-2 rounded-lg py-2.5 px-3 text-sm font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-500 data-[state=active]:text-white data-[state=active]:hover:from-green-600 data-[state=active]:hover:to-emerald-600"
                            >
                                <Smartphone className="h-4 w-4" />
                                <span>Mobile</span>
                            </TabsTrigger>
                        </TabsList>
                    </Tabs>
                </div>
            </div>

            {/* Premium Preview Area */}
            <div className="border border-slate-200 dark:border-slate-700 rounded-2xl p-6 bg-gradient-to-br from-slate-50 to-white dark:from-slate-900 dark:to-slate-800 overflow-visible shadow-lg">
                {previewMode === "desktop" ? (
                    <MockWebsite isMobile={false} />
                ) : (
                    <div className="flex justify-center">
                        <div className="w-80">
                            <MockWebsite isMobile={true} />
                        </div>
                    </div>
                )}
            </div>

            {/* Preview Info */}
            <div className="bg-slate-50 dark:bg-slate-800 rounded-xl p-4 space-y-2 text-sm">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <span className="font-semibold text-slate-700 dark:text-slate-300">Integration:</span>
                        <p className="text-slate-600 dark:text-slate-400">
                            {format === "html" && "JavaScript SDK"}
                            {format === "react" && "React Component"}
                            {format === "iframe" && "iFrame Embed"}
                        </p>
                    </div>
                    <div>
                        <span className="font-semibold text-slate-700 dark:text-slate-300">Position:</span>
                        <p className="text-slate-600 dark:text-slate-400">Bottom Right (customizable)</p>
                    </div>
                    <div>
                        <span className="font-semibold text-slate-700 dark:text-slate-300">Theme:</span>
                        <p className="text-slate-600 dark:text-slate-400">Auto (follows system preference)</p>
                    </div>
                </div>
            </div>

            {/* Reset Widget State */}
            {!widgetOpen && (
                <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setWidgetOpen(true)}
                    className="w-full border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800"
                >
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Show Widget Again
                </Button>
            )}
        </div>
    )
} 