"use client"

import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { AppearanceConfig } from "@/components/widget/appearance-config"
import { BehaviorConfig } from "@/components/widget/behavior-config"
import { ContentConfig } from "@/components/widget/content-config"
import { WidgetPreview } from "@/components/widget/widget-preview"
import { EnhancedWidgetPreview } from "@/components/widget/enhanced-widget-preview"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { useWidgetConfig } from "@/hooks/use-widget-config"
import { Palette, Settings, FileText, ExternalLink } from "lucide-react"

export function WidgetPanelLayout() {
    const { config, updateConfig, updateQuickReply, addQuickReply, removeQuickReply } = useWidgetConfig()

    return (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
            {/* Left Panel - Configuration */}
            <Card className="p-6 overflow-auto">
                <div className="space-y-6">
                    <div>
                        <h2 className="text-2xl font-semibold tracking-tight">Widget Configuration</h2>
                        <p className="text-muted-foreground">
                            Customize your chat widget&apos;s appearance and behavior
                        </p>
                    </div>

                    <Tabs defaultValue="appearance" className="w-full space-y-6">
                        {/* Enhanced Tab Navigation */}
                        <div className="bg-white dark:bg-slate-900 rounded-2xl shadow-lg border border-slate-200 dark:border-slate-700 p-1">
                            <TabsList className="grid w-full grid-cols-3 bg-transparent gap-1 h-auto p-1">
                                <TabsTrigger
                                    value="appearance"
                                    className="flex items-center justify-center gap-2 rounded-lg py-2.5 px-3 text-sm font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white data-[state=active]:hover:from-blue-600 data-[state=active]:hover:to-purple-600"
                                >
                                    <Palette className="h-4 w-4" />
                                    <span>Appearance</span>
                                </TabsTrigger>
                                <TabsTrigger
                                    value="behavior"
                                    className="flex items-center justify-center gap-2 rounded-lg py-2.5 px-3 text-sm font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-500 data-[state=active]:text-white data-[state=active]:hover:from-green-600 data-[state=active]:hover:to-emerald-600"
                                >
                                    <Settings className="h-4 w-4" />
                                    <span>Behavior</span>
                                </TabsTrigger>
                                <TabsTrigger
                                    value="content"
                                    className="flex items-center justify-center gap-2 rounded-lg py-2.5 px-3 text-sm font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white data-[state=active]:hover:from-orange-600 data-[state=active]:hover:to-red-600"
                                >
                                    <FileText className="h-4 w-4" />
                                    <span>Content</span>
                                </TabsTrigger>
                            </TabsList>
                        </div>

                        <TabsContent value="appearance" className="space-y-6">
                            <AppearanceConfig config={config} updateConfig={updateConfig} />
                        </TabsContent>

                        <TabsContent value="behavior" className="space-y-6">
                            <BehaviorConfig config={config} updateConfig={updateConfig} />
                        </TabsContent>

                        <TabsContent value="content" className="space-y-6">
                            <ContentConfig
                                config={config}
                                updateConfig={updateConfig}
                                updateQuickReply={updateQuickReply}
                                addQuickReply={addQuickReply}
                                removeQuickReply={removeQuickReply}
                            />
                        </TabsContent>
                    </Tabs>
                </div>
            </Card>

            {/* Right Panel - Live Preview */}
            <Card className="p-6 bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 border border-slate-200 dark:border-slate-700 shadow-lg">
                <div className="space-y-4">
                    <div className="flex items-center justify-between">
                        <div>
                            <h2 className="text-2xl font-semibold tracking-tight">Live Preview</h2>
                            <p className="text-muted-foreground">
                                See how your widget will appear to users
                            </p>
                        </div>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="gap-1.5"
                          onClick={() => window.open('#', '_blank')}
                        >
                          <ExternalLink className="h-4 w-4" />
                          <span>Full Preview</span>
                        </Button>
                    </div>

                    <div className="flex-1 flex items-center justify-center">
                        <EnhancedWidgetPreview config={config} />
                    </div>
                </div>
            </Card>
        </div>
    )
}