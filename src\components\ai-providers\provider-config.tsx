"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ArrowLeft, Eye, EyeOff, TestTube, Save, AlertTriangle } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface Provider {
    id: string
    name: string
    description: string
    logo: string
    status: "connected" | "disconnected"
    models: string[]
    pricing: string
    features: string[]
    isPopular: boolean
}

interface ProviderConfigProps {
    provider: Provider
    onBack: () => void
}

export function ProviderConfig({ provider, onBack }: ProviderConfigProps) {
    const [showApiKey, setShowApiKey] = useState(false)
    const [isConnecting, setIsConnecting] = useState(false)
    const [config, setConfig] = useState({
        apiKey: "",
        baseUrl: "",
        organization: "",
        defaultModel: "",
        maxTokens: 1000,
        temperature: 0.7,
        enableStreaming: true,
        enableFunctionCalling: false,
        rateLimitRpm: 60,
        rateLimitTpm: 10000,
    })

    const handleConnect = async () => {
        setIsConnecting(true)
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 2000))
        setIsConnecting(false)
        // Handle success/error
    }

    const getProviderSpecificFields = () => {
        switch (provider.id) {
            case "openai":
                return (
                    <>
                        <div className="space-y-2">
                            <Label htmlFor="organization">Organization ID (Optional)</Label>
                            <Input
                                id="organization"
                                value={config.organization}
                                onChange={(e) => setConfig(prev => ({ ...prev, organization: e.target.value }))}
                                placeholder="org-xxxxxxxxxxxxxxxxxx"
                            />
                        </div>
                    </>
                )
            case "azure":
                return (
                    <>
                        <div className="space-y-2">
                            <Label htmlFor="baseUrl">Azure Endpoint</Label>
                            <Input
                                id="baseUrl"
                                value={config.baseUrl}
                                onChange={(e) => setConfig(prev => ({ ...prev, baseUrl: e.target.value }))}
                                placeholder="https://your-resource.openai.azure.com/"
                                required
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="deployment">Deployment Name</Label>
                            <Input
                                id="deployment"
                                placeholder="your-deployment-name"
                                required
                            />
                        </div>
                    </>
                )
            case "anthropic":
                return (
                    <>
                        <div className="space-y-2">
                            <Label htmlFor="version">API Version</Label>
                            <Select defaultValue="2023-06-01">
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="2023-06-01">2023-06-01</SelectItem>
                                    <SelectItem value="2023-01-01">2023-01-01</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </>
                )
            default:
                return null
        }
    }

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center gap-4">
                <Button variant="outline" size="icon" onClick={onBack}>
                    <ArrowLeft className="h-4 w-4" />
                </Button>
                <div className="flex items-center gap-3">
                    <div className="text-2xl">{provider.logo}</div>
                    <div>
                        <h1 className="text-2xl font-bold">{provider.name} Configuration</h1>
                        <p className="text-muted-foreground">{provider.description}</p>
                    </div>
                </div>
            </div>

            <Tabs defaultValue="credentials" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="credentials">Credentials</TabsTrigger>
                    <TabsTrigger value="models">Models & Settings</TabsTrigger>
                    <TabsTrigger value="limits">Rate Limits</TabsTrigger>
                </TabsList>

                <TabsContent value="credentials" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>API Credentials</CardTitle>
                            <CardDescription>
                                Configure your API credentials to connect to {provider.name}
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="apiKey">API Key</Label>
                                <div className="relative">
                                    <Input
                                        id="apiKey"
                                        type={showApiKey ? "text" : "password"}
                                        value={config.apiKey}
                                        onChange={(e) => setConfig(prev => ({ ...prev, apiKey: e.target.value }))}
                                        placeholder="Enter your API key"
                                        className="pr-10"
                                        required
                                    />
                                    <Button
                                        type="button"
                                        variant="ghost"
                                        size="icon"
                                        className="absolute right-0 top-0 h-full px-3"
                                        onClick={() => setShowApiKey(!showApiKey)}
                                    >
                                        {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                                    </Button>
                                </div>
                            </div>

                            {getProviderSpecificFields()}

                            <Alert>
                                <AlertTriangle className="h-4 w-4" />
                                <AlertDescription>
                                    Your API keys are encrypted and stored securely. They are only used to make requests to {provider.name}.
                                </AlertDescription>
                            </Alert>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Connection Test</CardTitle>
                            <CardDescription>
                                Test your credentials before saving
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="flex gap-2">
                                <Button
                                    variant="outline"
                                    disabled={!config.apiKey || isConnecting}
                                    onClick={handleConnect}
                                >
                                    <TestTube className="h-4 w-4 mr-2" />
                                    {isConnecting ? "Testing..." : "Test Connection"}
                                </Button>
                                <Button
                                    disabled={!config.apiKey}
                                    onClick={handleConnect}
                                >
                                    <Save className="h-4 w-4 mr-2" />
                                    Save Configuration
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="models" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Model Configuration</CardTitle>
                            <CardDescription>
                                Configure default model and generation parameters
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="defaultModel">Default Model</Label>
                                <Select value={config.defaultModel} onValueChange={(value) => setConfig(prev => ({ ...prev, defaultModel: value }))}>
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select a model" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {provider.models.map((model) => (
                                            <SelectItem key={model} value={model}>
                                                {model}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="maxTokens">Max Tokens</Label>
                                    <Input
                                        id="maxTokens"
                                        type="number"
                                        value={config.maxTokens}
                                        onChange={(e) => setConfig(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
                                        min="1"
                                        max="4000"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="temperature">Temperature</Label>
                                    <Input
                                        id="temperature"
                                        type="number"
                                        value={config.temperature}
                                        onChange={(e) => setConfig(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
                                        min="0"
                                        max="2"
                                        step="0.1"
                                    />
                                </div>
                            </div>

                            <div className="space-y-4">
                                <div className="flex items-center justify-between">
                                    <div className="space-y-0.5">
                                        <Label htmlFor="streaming">Enable Streaming</Label>
                                        <p className="text-sm text-muted-foreground">
                                            Stream responses for better user experience
                                        </p>
                                    </div>
                                    <Switch
                                        id="streaming"
                                        checked={config.enableStreaming}
                                        onCheckedChange={(checked) => setConfig(prev => ({ ...prev, enableStreaming: checked }))}
                                    />
                                </div>

                                {provider.features.includes("Function Calling") && (
                                    <div className="flex items-center justify-between">
                                        <div className="space-y-0.5">
                                            <Label htmlFor="functions">Function Calling</Label>
                                            <p className="text-sm text-muted-foreground">
                                                Enable function calling capabilities
                                            </p>
                                        </div>
                                        <Switch
                                            id="functions"
                                            checked={config.enableFunctionCalling}
                                            onCheckedChange={(checked) => setConfig(prev => ({ ...prev, enableFunctionCalling: checked }))}
                                        />
                                    </div>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>

                <TabsContent value="limits" className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Rate Limits</CardTitle>
                            <CardDescription>
                                Configure rate limiting to manage API usage and costs
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="rateLimitRpm">Requests per Minute</Label>
                                    <Input
                                        id="rateLimitRpm"
                                        type="number"
                                        value={config.rateLimitRpm}
                                        onChange={(e) => setConfig(prev => ({ ...prev, rateLimitRpm: parseInt(e.target.value) }))}
                                        min="1"
                                        max="1000"
                                    />
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="rateLimitTpm">Tokens per Minute</Label>
                                    <Input
                                        id="rateLimitTpm"
                                        type="number"
                                        value={config.rateLimitTpm}
                                        onChange={(e) => setConfig(prev => ({ ...prev, rateLimitTpm: parseInt(e.target.value) }))}
                                        min="1000"
                                        max="100000"
                                    />
                                </div>
                            </div>

                            <Alert>
                                <AlertTriangle className="h-4 w-4" />
                                <AlertDescription>
                                    Rate limits help prevent unexpected charges and ensure fair usage across your applications.
                                </AlertDescription>
                            </Alert>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Usage Monitoring</CardTitle>
                            <CardDescription>
                                Monitor your API usage and costs
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-3 gap-4">
                                <div className="text-center">
                                    <div className="text-2xl font-bold">1,247</div>
                                    <div className="text-sm text-muted-foreground">Requests Today</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold">$12.34</div>
                                    <div className="text-sm text-muted-foreground">Cost This Month</div>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold">98.5%</div>
                                    <div className="text-sm text-muted-foreground">Success Rate</div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    )
} 