# Widget Builder Components

This directory contains the UI components for the widget builder interface. These components provide a professional, modern interface for configuring chat widgets without any business logic implementation.

## Components Overview

### `WidgetPanelLayout.tsx`
The main layout component that orchestrates the split-panel design:
- **Left Panel**: Configuration tabs (Appearance, Behavior, Content)
- **Right Panel**: Live preview with device mockups
- **Responsive**: Stacks vertically on mobile devices

### `AppearanceConfig.tsx`
Configuration panel for visual styling:
- **Theme Colors**: Primary, secondary, background, text colors with color pickers
- **Typography**: Font family and size selection
- **Border & Shape**: Border radius, width, and color customization
- **Shadow & Effects**: Shadow size options for visual depth

### `BehaviorConfig.tsx`
Configuration panel for widget behavior:
- **Widget Position**: Placement on website (bottom-right, bottom-left, etc.)
- **Trigger Settings**: Auto-open, mobile display, minimize options
- **Interaction Settings**: Sound notifications, typing indicators, timestamps
- **Advanced Settings**: Z-index, animation styles

### `ContentConfig.tsx`
Configuration panel for text content:
- **Welcome Message**: Title, message, and display timing
- **Input Field**: Placeholder text and character limits
- **Button Configuration**: Customizable button text
- **Quick Replies**: Pre-defined response options
- **Error Messages**: Connection errors, typing indicators, offline messages
- **Branding**: Company name, logo, powered-by text

### `WidgetPreview.tsx`
Interactive preview component:
- **Device Mockups**: Desktop browser and mobile phone frames
- **Interactive States**: Open, closed, minimized widget states
- **Realistic Design**: Actual chat interface with messages, avatars, typing indicators
- **Preview Controls**: Toggle widget states for testing

## Design Features

### Visual Design
- **Clean & Modern**: SaaS-grade interface with proper spacing and hierarchy
- **Consistent Styling**: Uses ShadCN UI components and Tailwind utilities
- **Responsive Layout**: Works on all screen sizes
- **Dark Mode Support**: Full theme compatibility

### User Experience
- **Intuitive Navigation**: Tab-based configuration organization
- **Visual Feedback**: Interactive preview updates in real-time
- **Professional Layout**: Card-based sections with clear descriptions
- **Accessibility**: Proper labels, ARIA attributes, keyboard navigation

### Technical Implementation
- **No Business Logic**: Pure UI components ready for state management
- **TypeScript**: Full type safety and IntelliSense support
- **Modular Structure**: Each component is self-contained and reusable
- **Performance Optimized**: Efficient rendering with React best practices

## Usage

```tsx
import { WidgetPanelLayout } from "@/components/widget/widget-panel-layout"

export default function WidgetBuilderPage() {
  return (
    <div className="h-full">
      <WidgetPanelLayout />
    </div>
  )
}
```

## Future Integration

These components are designed to be easily integrated with:
- State management (Redux, Zustand, etc.)
- Form libraries (React Hook Form, Formik)
- API integration for saving/loading configurations
- Real-time preview updates based on configuration changes

The component structure allows for easy extension and customization while maintaining the professional design standards. 