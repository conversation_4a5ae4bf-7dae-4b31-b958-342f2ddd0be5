"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Copy, Check, Download } from "lucide-react"
import { toast } from "sonner"

interface EmbedCodeBlockProps {
    format: string
    widgetId: string
    apiKey: string
}

export function EmbedCodeBlock({ format, widgetId, apiKey }: EmbedCodeBlockProps) {
    const [copied, setCopied] = useState(false)

    const getCodeSnippet = () => {
        switch (format) {
            case "html":
                return `<!-- Chat Widget Script -->
<script>
  (function() {
    var script = document.createElement('script');
    script.src = 'https://cdn.chatwidget.com/widget.js';
    script.async = true;
    script.onload = function() {
      ChatWidget.init({
        widgetId: '${widgetId}',
        apiKey: '${apiKey}',
        theme: 'auto',
        position: 'bottom-right'
      });
    };
    document.head.appendChild(script);
  })();
</script>`

            case "react":
                return `import { ChatWidget } from '@your-company/chat-widget-react';

function App() {
  return (
    <div>
      {/* Your app content */}
      
      <ChatWidget
        widgetId="${widgetId}"
        apiKey="${apiKey}"
        theme="auto"
        position="bottom-right"
        config={{
          welcomeMessage: "Hello! How can I help you today?",
          primaryColor: "#3b82f6",
          showPoweredBy: true
        }}
      />
    </div>
  );
}`

            case "iframe":
                return `<!-- Chat Widget iFrame -->
<iframe
  src="https://widget.chatwidget.com/embed/${widgetId}?key=${apiKey}&theme=auto"
  width="400"
  height="600"
  frameborder="0"
  style="
    position: fixed;
    bottom: 20px;
    right: 20px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 9999;
  "
  title="Chat Widget"
></iframe>`

            default:
                return ""
        }
    }

    const getLanguage = () => {
        switch (format) {
            case "html":
                return "html"
            case "react":
                return "jsx"
            case "iframe":
                return "html"
            default:
                return "text"
        }
    }

    const handleCopy = async () => {
        try {
            await navigator.clipboard.writeText(getCodeSnippet())
            setCopied(true)
            toast.success("Code copied!", {
                description: "The embed code has been copied to your clipboard.",
            })
            setTimeout(() => setCopied(false), 2000)
        } catch {
            // Fallback if clipboard API fails
            const textArea = document.createElement('textarea')
            textArea.value = getCodeSnippet()
            document.body.appendChild(textArea)
            textArea.select()
            document.execCommand('copy')
            document.body.removeChild(textArea)
        }
    }

    const handleDownload = () => {
        const code = getCodeSnippet()
        const extension = format === "react" ? "jsx" : "html"
        const filename = `chat-widget-embed.${extension}`

        const blob = new Blob([code], { type: "text/plain" })
        const url = URL.createObjectURL(blob)
        const a = document.createElement("a")
        a.href = url
        a.download = filename
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

        toast.success("File downloaded!", {
            description: `${filename} has been downloaded to your computer.`,
        })
    }

    return (
        <div className="space-y-6">
            {/* Header with actions */}
            <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                    <Badge variant="secondary" className="font-mono text-xs px-3 py-1">
                        {getLanguage()}
                    </Badge>
                    <span className="text-sm text-muted-foreground">
                        {format === "html" && "Vanilla JavaScript"}
                        {format === "react" && "React Component"}
                        {format === "iframe" && "HTML iFrame"}
                    </span>
                </div>
                <div className="flex items-center gap-3">
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={handleDownload}
                        className="h-9 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800"
                    >
                        <Download className="h-3 w-3 mr-2" />
                        Download
                    </Button>
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={handleCopy}
                        className="h-9 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800"
                    >
                        {copied ? (
                            <>
                                <Check className="h-3 w-3 mr-2 text-green-600" />
                                Copied!
                            </>
                        ) : (
                            <>
                                <Copy className="h-3 w-3 mr-2" />
                                Copy
                            </>
                        )}
                    </Button>
                </div>
            </div>

            {/* Code block */}
            <div className="relative">
                <pre className="bg-slate-50 dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-xl p-6 text-sm overflow-x-auto shadow-inner">
                    <code className="font-mono text-sm leading-relaxed text-slate-800 dark:text-slate-200">
                        {getCodeSnippet()}
                    </code>
                </pre>

                {/* Copy overlay button */}
                <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopy}
                    className="absolute top-3 right-3 h-8 w-8 p-0 opacity-0 hover:opacity-100 transition-opacity bg-white/80 dark:bg-slate-700/80 backdrop-blur-sm border border-slate-200 dark:border-slate-600"
                >
                    {copied ? (
                        <Check className="h-3 w-3 text-green-600" />
                    ) : (
                        <Copy className="h-3 w-3" />
                    )}
                </Button>
            </div>

            {/* Additional info */}
            <div className="bg-slate-50 dark:bg-slate-800 rounded-xl p-4 border border-slate-200 dark:border-slate-700">
                <div className="text-xs text-muted-foreground space-y-2">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <p>
                                <strong className="text-slate-700 dark:text-slate-300">Widget ID:</strong> {widgetId}
                            </p>
                            <p>
                                <strong className="text-slate-700 dark:text-slate-300">Environment:</strong> Production
                            </p>
                        </div>
                        {format === "react" && (
                            <div>
                                <p>
                                    <strong className="text-slate-700 dark:text-slate-300">Package:</strong> npm install @your-company/chat-widget-react
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    )
} 