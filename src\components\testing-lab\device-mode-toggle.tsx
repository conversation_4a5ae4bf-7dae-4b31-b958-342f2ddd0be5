"use client"

import { But<PERSON> } from "@/components/ui/button"
import { Monitor, Tablet, Smartphone } from "lucide-react"
import { DeviceMode } from "@/app/testing-lab/page"

interface DeviceModeToggleProps {
    deviceMode: DeviceMode
    onDeviceModeChange: (mode: DeviceMode) => void
}

export function DeviceModeToggle({ deviceMode, onDeviceModeChange }: DeviceModeToggleProps) {
    const devices: Array<{
        mode: DeviceMode
        icon: React.ReactNode
        label: string
        description: string
    }> = [
            {
                mode: "desktop",
                icon: <Monitor className="h-4 w-4" />,
                label: "Desktop",
                description: "1920×1080"
            },
            {
                mode: "tablet",
                icon: <Tablet className="h-4 w-4" />,
                label: "Tablet",
                description: "768×1024"
            },
            {
                mode: "mobile",
                icon: <Smartphone className="h-4 w-4" />,
                label: "Mobile",
                description: "375×812"
            }
        ]

    return (
        <div className="space-y-3">
            {devices.map((device) => (
                <Button
                    key={device.mode}
                    variant={deviceMode === device.mode ? "default" : "outline"}
                    className="w-full justify-start h-auto p-3"
                    onClick={() => onDeviceModeChange(device.mode)}
                >
                    <div className="flex items-center gap-3 w-full">
                        <div className="flex-shrink-0">
                            {device.icon}
                        </div>
                        <div className="flex-1 text-left">
                            <div className="font-medium text-sm">{device.label}</div>
                            <div className="text-xs text-muted-foreground font-mono">
                                {device.description}
                            </div>
                        </div>
                        {deviceMode === device.mode && (
                            <div className="w-2 h-2 bg-green-500 rounded-full flex-shrink-0"></div>
                        )}
                    </div>
                </Button>
            ))}
        </div>
    )
} 