[2025-06-12 21:28:52] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = schema() and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = schema() and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(976): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(955): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#4 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#5 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#31 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(976): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(955): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#29 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#30 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#31 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#32 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#33 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#34 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#35 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#36 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#42 {main}
"} 
[2025-06-12 21:29:22] local.ERROR: SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = schema() and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it (Connection: mysql, SQL: select exists (select 1 from information_schema.tables where table_schema = schema() and table_name = 'migrations' and table_type in ('BASE TABLE', 'SYSTEM VERSIONED')) as `exists`) at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(976): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(955): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#4 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#5 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#31 {main}

[previous exception] [object] (PDOException(code: 2002): SQLSTATE[HY000] [2002] No connection could be made because the target machine actively refused it at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(85): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(48): Illuminate\\Database\\Connectors\\Connector->tryAgainIfCausedByLostConnection(Object(PDOException), 'mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#4 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#5 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(976): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(955): Illuminate\\Database\\Connection->tryAgainIfCausedByLostConnection(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->handleQueryException(Object(Illuminate\\Database\\QueryException), 'select exists (...', Array, Object(Closure))
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#29 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#30 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#31 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#32 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#33 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#34 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#35 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#36 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#42 {main}
"} 
[2025-06-12 21:29:34] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 're currently offline. Please leave a message and we'll get back to you soon.', `' at line 1 (Connection: mysql, SQL: create table `widgets` (`id` bigint unsigned not null auto_increment primary key, `user_id` bigint unsigned not null, `name` varchar(255) not null, `slug` varchar(255) not null, `primary_color` varchar(255) not null default '#3b82f6', `secondary_color` varchar(255) not null default '#64748b', `background_color` varchar(255) not null default '#ffffff', `text_color` varchar(255) not null default '#1f2937', `border_color` varchar(255) not null default '#e5e7eb', `font_family` varchar(255) not null default 'inter', `font_size` varchar(255) not null default 'medium', `border_radius` varchar(255) not null default 'medium', `border_width` varchar(255) not null default '1', `shadow_size` varchar(255) not null default 'medium', `position` varchar(255) not null default 'bottom-right', `widget_size` varchar(255) not null default 'medium', `margin_x` int not null default '20', `margin_y` int not null default '20', `auto_open` tinyint(1) not null default '0', `show_on_mobile` tinyint(1) not null default '1', `allow_minimize` tinyint(1) not null default '1', `auto_open_delay` int not null default '3', `sound_notifications` tinyint(1) not null default '0', `typing_indicator` tinyint(1) not null default '1', `message_timestamps` tinyint(1) not null default '1', `response_delay` int not null default '1', `z_index` int not null default '9999', `animation_style` varchar(255) not null default 'slide', `welcome_title` varchar(255) not null default 'Hi there! 👋', `welcome_message` text not null default 'How can I help you today?', `welcome_delay` int not null default '2', `input_placeholder` varchar(255) not null default 'Type your message here...', `max_message_length` int not null default '500', `chat_button_text` varchar(255) not null default 'Start Chat', `send_button_text` varchar(255) not null default 'Send', `close_button_text` varchar(255) not null default 'Close Chat', `minimize_button_text` varchar(255) not null default 'Minimize', `quick_replies` json null, `connection_error` varchar(255) not null default 'Unable to connect. Please check your internet connection.', `typing_message` varchar(255) not null default 'Assistant is typing...', `offline_message` text not null default 'We're currently offline. Please leave a message and we'll get back to you soon.', `company_name` varchar(255) not null default 'Acme Corp', `company_logo` varchar(255) null, `agent_name` varchar(255) not null default 'AI Assistant', `agent_avatar` varchar(255) null, `show_powered_by` tinyint(1) not null default '1', `powered_by_text` varchar(255) not null default 'Powered by ChatWidget Builder', `logo_url` varchar(255) null, `privacy_policy_url` varchar(255) null, `terms_of_service_url` varchar(255) null, `gdpr_compliance` tinyint(1) not null default '0', `is_active` tinyint(1) not null default '1', `views` int not null default '0', `interactions` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 're currently offline. Please leave a message and we'll get back to you soon.', `' at line 1 (Connection: mysql, SQL: create table `widgets` (`id` bigint unsigned not null auto_increment primary key, `user_id` bigint unsigned not null, `name` varchar(255) not null, `slug` varchar(255) not null, `primary_color` varchar(255) not null default '#3b82f6', `secondary_color` varchar(255) not null default '#64748b', `background_color` varchar(255) not null default '#ffffff', `text_color` varchar(255) not null default '#1f2937', `border_color` varchar(255) not null default '#e5e7eb', `font_family` varchar(255) not null default 'inter', `font_size` varchar(255) not null default 'medium', `border_radius` varchar(255) not null default 'medium', `border_width` varchar(255) not null default '1', `shadow_size` varchar(255) not null default 'medium', `position` varchar(255) not null default 'bottom-right', `widget_size` varchar(255) not null default 'medium', `margin_x` int not null default '20', `margin_y` int not null default '20', `auto_open` tinyint(1) not null default '0', `show_on_mobile` tinyint(1) not null default '1', `allow_minimize` tinyint(1) not null default '1', `auto_open_delay` int not null default '3', `sound_notifications` tinyint(1) not null default '0', `typing_indicator` tinyint(1) not null default '1', `message_timestamps` tinyint(1) not null default '1', `response_delay` int not null default '1', `z_index` int not null default '9999', `animation_style` varchar(255) not null default 'slide', `welcome_title` varchar(255) not null default 'Hi there! 👋', `welcome_message` text not null default 'How can I help you today?', `welcome_delay` int not null default '2', `input_placeholder` varchar(255) not null default 'Type your message here...', `max_message_length` int not null default '500', `chat_button_text` varchar(255) not null default 'Start Chat', `send_button_text` varchar(255) not null default 'Send', `close_button_text` varchar(255) not null default 'Close Chat', `minimize_button_text` varchar(255) not null default 'Minimize', `quick_replies` json null, `connection_error` varchar(255) not null default 'Unable to connect. Please check your internet connection.', `typing_message` varchar(255) not null default 'Assistant is typing...', `offline_message` text not null default 'We're currently offline. Please leave a message and we'll get back to you soon.', `company_name` varchar(255) not null default 'Acme Corp', `company_logo` varchar(255) null, `agent_name` varchar(255) not null default 'AI Assistant', `agent_avatar` varchar(255) null, `show_powered_by` tinyint(1) not null default '1', `powered_by_text` varchar(255) not null default 'Powered by ChatWidget Builder', `logo_url` varchar(255) null, `privacy_policy_url` varchar(255) null, `terms_of_service_url` varchar(255) null, `gdpr_compliance` tinyint(1) not null default '0', `is_active` tinyint(1) not null default '1', `views` int not null default '0', `interactions` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `w...', Array, Object(Closure))
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `w...', Array, Object(Closure))
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `w...')
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('widgets', Object(Closure))
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\database\\migrations\\2024_08_01_000001_create_widgets_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2024_08_01_0000...', Object(Closure))
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_08_01_0000...', Object(Closure))
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\laragon\\\\www\\\\...', 2, false)
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 're currently offline. Please leave a message and we'll get back to you soon.', `' at line 1 at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create table `w...')
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `w...', Array)
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `w...', Array, Object(Closure))
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `w...', Array, Object(Closure))
#4 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `w...')
#5 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('widgets', Object(Closure))
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\database\\migrations\\2024_08_01_000001_create_widgets_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2024_08_01_0000...', Object(Closure))
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_08_01_0000...', Object(Closure))
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\laragon\\\\www\\\\...', 2, false)
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-12 21:29:51] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 're currently offline. Please leave a message and we'll get back to you soon.', `' at line 1 (Connection: mysql, SQL: create table `widgets` (`id` bigint unsigned not null auto_increment primary key, `user_id` bigint unsigned not null, `name` varchar(255) not null, `slug` varchar(255) not null, `primary_color` varchar(255) not null default '#3b82f6', `secondary_color` varchar(255) not null default '#64748b', `background_color` varchar(255) not null default '#ffffff', `text_color` varchar(255) not null default '#1f2937', `border_color` varchar(255) not null default '#e5e7eb', `font_family` varchar(255) not null default 'inter', `font_size` varchar(255) not null default 'medium', `border_radius` varchar(255) not null default 'medium', `border_width` varchar(255) not null default '1', `shadow_size` varchar(255) not null default 'medium', `position` varchar(255) not null default 'bottom-right', `widget_size` varchar(255) not null default 'medium', `margin_x` int not null default '20', `margin_y` int not null default '20', `auto_open` tinyint(1) not null default '0', `show_on_mobile` tinyint(1) not null default '1', `allow_minimize` tinyint(1) not null default '1', `auto_open_delay` int not null default '3', `sound_notifications` tinyint(1) not null default '0', `typing_indicator` tinyint(1) not null default '1', `message_timestamps` tinyint(1) not null default '1', `response_delay` int not null default '1', `z_index` int not null default '9999', `animation_style` varchar(255) not null default 'slide', `welcome_title` varchar(255) not null default 'Hi there! 👋', `welcome_message` text not null default 'How can I help you today?', `welcome_delay` int not null default '2', `input_placeholder` varchar(255) not null default 'Type your message here...', `max_message_length` int not null default '500', `chat_button_text` varchar(255) not null default 'Start Chat', `send_button_text` varchar(255) not null default 'Send', `close_button_text` varchar(255) not null default 'Close Chat', `minimize_button_text` varchar(255) not null default 'Minimize', `quick_replies` json null, `connection_error` varchar(255) not null default 'Unable to connect. Please check your internet connection.', `typing_message` varchar(255) not null default 'Assistant is typing...', `offline_message` text not null default 'We're currently offline. Please leave a message and we'll get back to you soon.', `company_name` varchar(255) not null default 'Acme Corp', `company_logo` varchar(255) null, `agent_name` varchar(255) not null default 'AI Assistant', `agent_avatar` varchar(255) null, `show_powered_by` tinyint(1) not null default '1', `powered_by_text` varchar(255) not null default 'Powered by ChatWidget Builder', `logo_url` varchar(255) null, `privacy_policy_url` varchar(255) null, `terms_of_service_url` varchar(255) null, `gdpr_compliance` tinyint(1) not null default '0', `is_active` tinyint(1) not null default '1', `views` int not null default '0', `interactions` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 're currently offline. Please leave a message and we'll get back to you soon.', `' at line 1 (Connection: mysql, SQL: create table `widgets` (`id` bigint unsigned not null auto_increment primary key, `user_id` bigint unsigned not null, `name` varchar(255) not null, `slug` varchar(255) not null, `primary_color` varchar(255) not null default '#3b82f6', `secondary_color` varchar(255) not null default '#64748b', `background_color` varchar(255) not null default '#ffffff', `text_color` varchar(255) not null default '#1f2937', `border_color` varchar(255) not null default '#e5e7eb', `font_family` varchar(255) not null default 'inter', `font_size` varchar(255) not null default 'medium', `border_radius` varchar(255) not null default 'medium', `border_width` varchar(255) not null default '1', `shadow_size` varchar(255) not null default 'medium', `position` varchar(255) not null default 'bottom-right', `widget_size` varchar(255) not null default 'medium', `margin_x` int not null default '20', `margin_y` int not null default '20', `auto_open` tinyint(1) not null default '0', `show_on_mobile` tinyint(1) not null default '1', `allow_minimize` tinyint(1) not null default '1', `auto_open_delay` int not null default '3', `sound_notifications` tinyint(1) not null default '0', `typing_indicator` tinyint(1) not null default '1', `message_timestamps` tinyint(1) not null default '1', `response_delay` int not null default '1', `z_index` int not null default '9999', `animation_style` varchar(255) not null default 'slide', `welcome_title` varchar(255) not null default 'Hi there! 👋', `welcome_message` text not null default 'How can I help you today?', `welcome_delay` int not null default '2', `input_placeholder` varchar(255) not null default 'Type your message here...', `max_message_length` int not null default '500', `chat_button_text` varchar(255) not null default 'Start Chat', `send_button_text` varchar(255) not null default 'Send', `close_button_text` varchar(255) not null default 'Close Chat', `minimize_button_text` varchar(255) not null default 'Minimize', `quick_replies` json null, `connection_error` varchar(255) not null default 'Unable to connect. Please check your internet connection.', `typing_message` varchar(255) not null default 'Assistant is typing...', `offline_message` text not null default 'We're currently offline. Please leave a message and we'll get back to you soon.', `company_name` varchar(255) not null default 'Acme Corp', `company_logo` varchar(255) null, `agent_name` varchar(255) not null default 'AI Assistant', `agent_avatar` varchar(255) null, `show_powered_by` tinyint(1) not null default '1', `powered_by_text` varchar(255) not null default 'Powered by ChatWidget Builder', `logo_url` varchar(255) null, `privacy_policy_url` varchar(255) null, `terms_of_service_url` varchar(255) null, `gdpr_compliance` tinyint(1) not null default '0', `is_active` tinyint(1) not null default '1', `views` int not null default '0', `interactions` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `w...', Array, Object(Closure))
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `w...', Array, Object(Closure))
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `w...')
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('widgets', Object(Closure))
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\database\\migrations\\2024_08_01_000001_create_widgets_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2024_08_01_0000...', Object(Closure))
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_08_01_0000...', Object(Closure))
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\laragon\\\\www\\\\...', 2, false)
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 're currently offline. Please leave a message and we'll get back to you soon.', `' at line 1 at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create table `w...')
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `w...', Array)
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `w...', Array, Object(Closure))
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `w...', Array, Object(Closure))
#4 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `w...')
#5 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('widgets', Object(Closure))
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\database\\migrations\\2024_08_01_000001_create_widgets_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2024_08_01_0000...', Object(Closure))
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_08_01_0000...', Object(Closure))
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\laragon\\\\www\\\\...', 2, false)
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-12 21:30:27] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 're currently offline. Please leave a message and we'll get back to you soon.', `' at line 1 (Connection: mysql, SQL: create table `widgets` (`id` bigint unsigned not null auto_increment primary key, `user_id` bigint unsigned not null, `name` varchar(255) not null, `slug` varchar(255) not null, `primary_color` varchar(255) not null default '#3b82f6', `secondary_color` varchar(255) not null default '#64748b', `background_color` varchar(255) not null default '#ffffff', `text_color` varchar(255) not null default '#1f2937', `border_color` varchar(255) not null default '#e5e7eb', `font_family` varchar(255) not null default 'inter', `font_size` varchar(255) not null default 'medium', `border_radius` varchar(255) not null default 'medium', `border_width` varchar(255) not null default '1', `shadow_size` varchar(255) not null default 'medium', `position` varchar(255) not null default 'bottom-right', `widget_size` varchar(255) not null default 'medium', `margin_x` int not null default '20', `margin_y` int not null default '20', `auto_open` tinyint(1) not null default '0', `show_on_mobile` tinyint(1) not null default '1', `allow_minimize` tinyint(1) not null default '1', `auto_open_delay` int not null default '3', `sound_notifications` tinyint(1) not null default '0', `typing_indicator` tinyint(1) not null default '1', `message_timestamps` tinyint(1) not null default '1', `response_delay` int not null default '1', `z_index` int not null default '9999', `animation_style` varchar(255) not null default 'slide', `welcome_title` varchar(255) not null default 'Hi there! 👋', `welcome_message` text not null default 'How can I help you today?', `welcome_delay` int not null default '2', `input_placeholder` varchar(255) not null default 'Type your message here...', `max_message_length` int not null default '500', `chat_button_text` varchar(255) not null default 'Start Chat', `send_button_text` varchar(255) not null default 'Send', `close_button_text` varchar(255) not null default 'Close Chat', `minimize_button_text` varchar(255) not null default 'Minimize', `quick_replies` json null, `connection_error` varchar(255) not null default 'Unable to connect. Please check your internet connection.', `typing_message` varchar(255) not null default 'Assistant is typing...', `offline_message` text not null default 'We're currently offline. Please leave a message and we'll get back to you soon.', `company_name` varchar(255) not null default 'Acme Corp', `company_logo` varchar(255) null, `agent_name` varchar(255) not null default 'AI Assistant', `agent_avatar` varchar(255) null, `show_powered_by` tinyint(1) not null default '1', `powered_by_text` varchar(255) not null default 'Powered by ChatWidget Builder', `logo_url` varchar(255) null, `privacy_policy_url` varchar(255) null, `terms_of_service_url` varchar(255) null, `gdpr_compliance` tinyint(1) not null default '0', `is_active` tinyint(1) not null default '1', `views` int not null default '0', `interactions` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 're currently offline. Please leave a message and we'll get back to you soon.', `' at line 1 (Connection: mysql, SQL: create table `widgets` (`id` bigint unsigned not null auto_increment primary key, `user_id` bigint unsigned not null, `name` varchar(255) not null, `slug` varchar(255) not null, `primary_color` varchar(255) not null default '#3b82f6', `secondary_color` varchar(255) not null default '#64748b', `background_color` varchar(255) not null default '#ffffff', `text_color` varchar(255) not null default '#1f2937', `border_color` varchar(255) not null default '#e5e7eb', `font_family` varchar(255) not null default 'inter', `font_size` varchar(255) not null default 'medium', `border_radius` varchar(255) not null default 'medium', `border_width` varchar(255) not null default '1', `shadow_size` varchar(255) not null default 'medium', `position` varchar(255) not null default 'bottom-right', `widget_size` varchar(255) not null default 'medium', `margin_x` int not null default '20', `margin_y` int not null default '20', `auto_open` tinyint(1) not null default '0', `show_on_mobile` tinyint(1) not null default '1', `allow_minimize` tinyint(1) not null default '1', `auto_open_delay` int not null default '3', `sound_notifications` tinyint(1) not null default '0', `typing_indicator` tinyint(1) not null default '1', `message_timestamps` tinyint(1) not null default '1', `response_delay` int not null default '1', `z_index` int not null default '9999', `animation_style` varchar(255) not null default 'slide', `welcome_title` varchar(255) not null default 'Hi there! 👋', `welcome_message` text not null default 'How can I help you today?', `welcome_delay` int not null default '2', `input_placeholder` varchar(255) not null default 'Type your message here...', `max_message_length` int not null default '500', `chat_button_text` varchar(255) not null default 'Start Chat', `send_button_text` varchar(255) not null default 'Send', `close_button_text` varchar(255) not null default 'Close Chat', `minimize_button_text` varchar(255) not null default 'Minimize', `quick_replies` json null, `connection_error` varchar(255) not null default 'Unable to connect. Please check your internet connection.', `typing_message` varchar(255) not null default 'Assistant is typing...', `offline_message` text not null default 'We're currently offline. Please leave a message and we'll get back to you soon.', `company_name` varchar(255) not null default 'Acme Corp', `company_logo` varchar(255) null, `agent_name` varchar(255) not null default 'AI Assistant', `agent_avatar` varchar(255) null, `show_powered_by` tinyint(1) not null default '1', `powered_by_text` varchar(255) not null default 'Powered by ChatWidget Builder', `logo_url` varchar(255) null, `privacy_policy_url` varchar(255) null, `terms_of_service_url` varchar(255) null, `gdpr_compliance` tinyint(1) not null default '0', `is_active` tinyint(1) not null default '1', `views` int not null default '0', `interactions` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `w...', Array, Object(Closure))
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `w...', Array, Object(Closure))
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `w...')
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('widgets', Object(Closure))
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\database\\migrations\\2024_08_01_000001_create_widgets_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2024_08_01_0000...', Object(Closure))
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_08_01_0000...', Object(Closure))
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\laragon\\\\www\\\\...', 2, false)
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1064 You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 're currently offline. Please leave a message and we'll get back to you soon.', `' at line 1 at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create table `w...')
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `w...', Array)
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `w...', Array, Object(Closure))
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `w...', Array, Object(Closure))
#4 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `w...')
#5 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('widgets', Object(Closure))
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\database\\migrations\\2024_08_01_000001_create_widgets_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2024_08_01_0000...', Object(Closure))
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_08_01_0000...', Object(Closure))
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\laragon\\\\www\\\\...', 2, false)
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-12 21:31:02] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1101 BLOB, TEXT, GEOMETRY or JSON column 'welcome_message' can't have a default value (Connection: mysql, SQL: create table `widgets` (`id` bigint unsigned not null auto_increment primary key, `user_id` bigint unsigned not null, `name` varchar(255) not null, `slug` varchar(255) not null, `primary_color` varchar(255) not null default '#3b82f6', `secondary_color` varchar(255) not null default '#64748b', `background_color` varchar(255) not null default '#ffffff', `text_color` varchar(255) not null default '#1f2937', `border_color` varchar(255) not null default '#e5e7eb', `font_family` varchar(255) not null default 'inter', `font_size` varchar(255) not null default 'medium', `border_radius` varchar(255) not null default 'medium', `border_width` varchar(255) not null default '1', `shadow_size` varchar(255) not null default 'medium', `position` varchar(255) not null default 'bottom-right', `widget_size` varchar(255) not null default 'medium', `margin_x` int not null default '20', `margin_y` int not null default '20', `auto_open` tinyint(1) not null default '0', `show_on_mobile` tinyint(1) not null default '1', `allow_minimize` tinyint(1) not null default '1', `auto_open_delay` int not null default '3', `sound_notifications` tinyint(1) not null default '0', `typing_indicator` tinyint(1) not null default '1', `message_timestamps` tinyint(1) not null default '1', `response_delay` int not null default '1', `z_index` int not null default '9999', `animation_style` varchar(255) not null default 'slide', `welcome_title` varchar(255) not null default 'Hi there! 👋', `welcome_message` text not null default 'How can I help you today?', `welcome_delay` int not null default '2', `input_placeholder` varchar(255) not null default 'Type your message here...', `max_message_length` int not null default '500', `chat_button_text` varchar(255) not null default 'Start Chat', `send_button_text` varchar(255) not null default 'Send', `close_button_text` varchar(255) not null default 'Close Chat', `minimize_button_text` varchar(255) not null default 'Minimize', `quick_replies` json null, `connection_error` varchar(255) not null default 'Unable to connect. Please check your internet connection.', `typing_message` varchar(255) not null default 'Assistant is typing...', `offline_message` text not null default 'We are currently offline. Please leave a message and we will get back to you soon.', `company_name` varchar(255) not null default 'Acme Corp', `company_logo` varchar(255) null, `agent_name` varchar(255) not null default 'AI Assistant', `agent_avatar` varchar(255) null, `show_powered_by` tinyint(1) not null default '1', `powered_by_text` varchar(255) not null default 'Powered by ChatWidget Builder', `logo_url` varchar(255) null, `privacy_policy_url` varchar(255) null, `terms_of_service_url` varchar(255) null, `gdpr_compliance` tinyint(1) not null default '0', `is_active` tinyint(1) not null default '1', `views` int not null default '0', `interactions` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1101 BLOB, TEXT, GEOMETRY or JSON column 'welcome_message' can't have a default value (Connection: mysql, SQL: create table `widgets` (`id` bigint unsigned not null auto_increment primary key, `user_id` bigint unsigned not null, `name` varchar(255) not null, `slug` varchar(255) not null, `primary_color` varchar(255) not null default '#3b82f6', `secondary_color` varchar(255) not null default '#64748b', `background_color` varchar(255) not null default '#ffffff', `text_color` varchar(255) not null default '#1f2937', `border_color` varchar(255) not null default '#e5e7eb', `font_family` varchar(255) not null default 'inter', `font_size` varchar(255) not null default 'medium', `border_radius` varchar(255) not null default 'medium', `border_width` varchar(255) not null default '1', `shadow_size` varchar(255) not null default 'medium', `position` varchar(255) not null default 'bottom-right', `widget_size` varchar(255) not null default 'medium', `margin_x` int not null default '20', `margin_y` int not null default '20', `auto_open` tinyint(1) not null default '0', `show_on_mobile` tinyint(1) not null default '1', `allow_minimize` tinyint(1) not null default '1', `auto_open_delay` int not null default '3', `sound_notifications` tinyint(1) not null default '0', `typing_indicator` tinyint(1) not null default '1', `message_timestamps` tinyint(1) not null default '1', `response_delay` int not null default '1', `z_index` int not null default '9999', `animation_style` varchar(255) not null default 'slide', `welcome_title` varchar(255) not null default 'Hi there! 👋', `welcome_message` text not null default 'How can I help you today?', `welcome_delay` int not null default '2', `input_placeholder` varchar(255) not null default 'Type your message here...', `max_message_length` int not null default '500', `chat_button_text` varchar(255) not null default 'Start Chat', `send_button_text` varchar(255) not null default 'Send', `close_button_text` varchar(255) not null default 'Close Chat', `minimize_button_text` varchar(255) not null default 'Minimize', `quick_replies` json null, `connection_error` varchar(255) not null default 'Unable to connect. Please check your internet connection.', `typing_message` varchar(255) not null default 'Assistant is typing...', `offline_message` text not null default 'We are currently offline. Please leave a message and we will get back to you soon.', `company_name` varchar(255) not null default 'Acme Corp', `company_logo` varchar(255) null, `agent_name` varchar(255) not null default 'AI Assistant', `agent_avatar` varchar(255) null, `show_powered_by` tinyint(1) not null default '1', `powered_by_text` varchar(255) not null default 'Powered by ChatWidget Builder', `logo_url` varchar(255) null, `privacy_policy_url` varchar(255) null, `terms_of_service_url` varchar(255) null, `gdpr_compliance` tinyint(1) not null default '0', `is_active` tinyint(1) not null default '1', `views` int not null default '0', `interactions` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `w...', Array, Object(Closure))
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `w...', Array, Object(Closure))
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `w...')
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('widgets', Object(Closure))
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\database\\migrations\\2024_08_01_000001_create_widgets_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2024_08_01_0000...', Object(Closure))
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_08_01_0000...', Object(Closure))
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\laragon\\\\www\\\\...', 2, false)
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1101 BLOB, TEXT, GEOMETRY or JSON column 'welcome_message' can't have a default value at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create table `w...')
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `w...', Array)
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `w...', Array, Object(Closure))
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `w...', Array, Object(Closure))
#4 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `w...')
#5 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('widgets', Object(Closure))
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\database\\migrations\\2024_08_01_000001_create_widgets_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2024_08_01_0000...', Object(Closure))
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_08_01_0000...', Object(Closure))
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\laragon\\\\www\\\\...', 2, false)
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-12 21:31:23] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1101 BLOB, TEXT, GEOMETRY or JSON column 'welcome_message' can't have a default value (Connection: mysql, SQL: create table `widgets` (`id` bigint unsigned not null auto_increment primary key, `user_id` bigint unsigned not null, `name` varchar(255) not null, `slug` varchar(255) not null, `primary_color` varchar(255) not null default '#3b82f6', `secondary_color` varchar(255) not null default '#64748b', `background_color` varchar(255) not null default '#ffffff', `text_color` varchar(255) not null default '#1f2937', `border_color` varchar(255) not null default '#e5e7eb', `font_family` varchar(255) not null default 'inter', `font_size` varchar(255) not null default 'medium', `border_radius` varchar(255) not null default 'medium', `border_width` varchar(255) not null default '1', `shadow_size` varchar(255) not null default 'medium', `position` varchar(255) not null default 'bottom-right', `widget_size` varchar(255) not null default 'medium', `margin_x` int not null default '20', `margin_y` int not null default '20', `auto_open` tinyint(1) not null default '0', `show_on_mobile` tinyint(1) not null default '1', `allow_minimize` tinyint(1) not null default '1', `auto_open_delay` int not null default '3', `sound_notifications` tinyint(1) not null default '0', `typing_indicator` tinyint(1) not null default '1', `message_timestamps` tinyint(1) not null default '1', `response_delay` int not null default '1', `z_index` int not null default '9999', `animation_style` varchar(255) not null default 'slide', `welcome_title` varchar(255) not null default 'Hi there! 👋', `welcome_message` text not null default 'How can I help you today?', `welcome_delay` int not null default '2', `input_placeholder` varchar(255) not null default 'Type your message here...', `max_message_length` int not null default '500', `chat_button_text` varchar(255) not null default 'Start Chat', `send_button_text` varchar(255) not null default 'Send', `close_button_text` varchar(255) not null default 'Close Chat', `minimize_button_text` varchar(255) not null default 'Minimize', `quick_replies` json null, `connection_error` varchar(255) not null default 'Unable to connect. Please check your internet connection.', `typing_message` varchar(255) not null default 'Assistant is typing...', `offline_message` text not null default 'We are currently offline. Please leave a message and we will get back to you soon.', `company_name` varchar(255) not null default 'Acme Corp', `company_logo` varchar(255) null, `agent_name` varchar(255) not null default 'AI Assistant', `agent_avatar` varchar(255) null, `show_powered_by` tinyint(1) not null default '1', `powered_by_text` varchar(255) not null default 'Powered by ChatWidget Builder', `logo_url` varchar(255) null, `privacy_policy_url` varchar(255) null, `terms_of_service_url` varchar(255) null, `gdpr_compliance` tinyint(1) not null default '0', `is_active` tinyint(1) not null default '1', `views` int not null default '0', `interactions` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1101 BLOB, TEXT, GEOMETRY or JSON column 'welcome_message' can't have a default value (Connection: mysql, SQL: create table `widgets` (`id` bigint unsigned not null auto_increment primary key, `user_id` bigint unsigned not null, `name` varchar(255) not null, `slug` varchar(255) not null, `primary_color` varchar(255) not null default '#3b82f6', `secondary_color` varchar(255) not null default '#64748b', `background_color` varchar(255) not null default '#ffffff', `text_color` varchar(255) not null default '#1f2937', `border_color` varchar(255) not null default '#e5e7eb', `font_family` varchar(255) not null default 'inter', `font_size` varchar(255) not null default 'medium', `border_radius` varchar(255) not null default 'medium', `border_width` varchar(255) not null default '1', `shadow_size` varchar(255) not null default 'medium', `position` varchar(255) not null default 'bottom-right', `widget_size` varchar(255) not null default 'medium', `margin_x` int not null default '20', `margin_y` int not null default '20', `auto_open` tinyint(1) not null default '0', `show_on_mobile` tinyint(1) not null default '1', `allow_minimize` tinyint(1) not null default '1', `auto_open_delay` int not null default '3', `sound_notifications` tinyint(1) not null default '0', `typing_indicator` tinyint(1) not null default '1', `message_timestamps` tinyint(1) not null default '1', `response_delay` int not null default '1', `z_index` int not null default '9999', `animation_style` varchar(255) not null default 'slide', `welcome_title` varchar(255) not null default 'Hi there! 👋', `welcome_message` text not null default 'How can I help you today?', `welcome_delay` int not null default '2', `input_placeholder` varchar(255) not null default 'Type your message here...', `max_message_length` int not null default '500', `chat_button_text` varchar(255) not null default 'Start Chat', `send_button_text` varchar(255) not null default 'Send', `close_button_text` varchar(255) not null default 'Close Chat', `minimize_button_text` varchar(255) not null default 'Minimize', `quick_replies` json null, `connection_error` varchar(255) not null default 'Unable to connect. Please check your internet connection.', `typing_message` varchar(255) not null default 'Assistant is typing...', `offline_message` text not null default 'We are currently offline. Please leave a message and we will get back to you soon.', `company_name` varchar(255) not null default 'Acme Corp', `company_logo` varchar(255) null, `agent_name` varchar(255) not null default 'AI Assistant', `agent_avatar` varchar(255) null, `show_powered_by` tinyint(1) not null default '1', `powered_by_text` varchar(255) not null default 'Powered by ChatWidget Builder', `logo_url` varchar(255) null, `privacy_policy_url` varchar(255) null, `terms_of_service_url` varchar(255) null, `gdpr_compliance` tinyint(1) not null default '0', `is_active` tinyint(1) not null default '1', `views` int not null default '0', `interactions` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `w...', Array, Object(Closure))
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `w...', Array, Object(Closure))
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `w...')
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('widgets', Object(Closure))
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\database\\migrations\\2024_08_01_000001_create_widgets_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2024_08_01_0000...', Object(Closure))
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_08_01_0000...', Object(Closure))
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\laragon\\\\www\\\\...', 2, false)
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1101 BLOB, TEXT, GEOMETRY or JSON column 'welcome_message' can't have a default value at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create table `w...')
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `w...', Array)
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `w...', Array, Object(Closure))
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `w...', Array, Object(Closure))
#4 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `w...')
#5 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('widgets', Object(Closure))
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\database\\migrations\\2024_08_01_000001_create_widgets_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2024_08_01_0000...', Object(Closure))
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_08_01_0000...', Object(Closure))
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\laragon\\\\www\\\\...', 2, false)
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-12 21:31:50] local.ERROR: SQLSTATE[42000]: Syntax error or access violation: 1101 BLOB, TEXT, GEOMETRY or JSON column 'welcome_message' can't have a default value (Connection: mysql, SQL: create table `widgets` (`id` bigint unsigned not null auto_increment primary key, `user_id` bigint unsigned not null, `name` varchar(255) not null, `slug` varchar(255) not null, `primary_color` varchar(255) not null default '#3b82f6', `secondary_color` varchar(255) not null default '#64748b', `background_color` varchar(255) not null default '#ffffff', `text_color` varchar(255) not null default '#1f2937', `border_color` varchar(255) not null default '#e5e7eb', `font_family` varchar(255) not null default 'inter', `font_size` varchar(255) not null default 'medium', `border_radius` varchar(255) not null default 'medium', `border_width` varchar(255) not null default '1', `shadow_size` varchar(255) not null default 'medium', `position` varchar(255) not null default 'bottom-right', `widget_size` varchar(255) not null default 'medium', `margin_x` int not null default '20', `margin_y` int not null default '20', `auto_open` tinyint(1) not null default '0', `show_on_mobile` tinyint(1) not null default '1', `allow_minimize` tinyint(1) not null default '1', `auto_open_delay` int not null default '3', `sound_notifications` tinyint(1) not null default '0', `typing_indicator` tinyint(1) not null default '1', `message_timestamps` tinyint(1) not null default '1', `response_delay` int not null default '1', `z_index` int not null default '9999', `animation_style` varchar(255) not null default 'slide', `welcome_title` varchar(255) not null default 'Hi there! 👋', `welcome_message` text not null default 'How can I help you today?', `welcome_delay` int not null default '2', `input_placeholder` varchar(255) not null default 'Type your message here...', `max_message_length` int not null default '500', `chat_button_text` varchar(255) not null default 'Start Chat', `send_button_text` varchar(255) not null default 'Send', `close_button_text` varchar(255) not null default 'Close Chat', `minimize_button_text` varchar(255) not null default 'Minimize', `quick_replies` json null, `connection_error` varchar(255) not null default 'Unable to connect. Please check your internet connection.', `typing_message` varchar(255) not null default 'Assistant is typing...', `offline_message` text not null default 'We are currently offline. Please leave a message and we will get back to you soon.', `company_name` varchar(255) not null default 'Acme Corp', `company_logo` varchar(255) null, `agent_name` varchar(255) not null default 'AI Assistant', `agent_avatar` varchar(255) null, `show_powered_by` tinyint(1) not null default '1', `powered_by_text` varchar(255) not null default 'Powered by ChatWidget Builder', `logo_url` varchar(255) null, `privacy_policy_url` varchar(255) null, `terms_of_service_url` varchar(255) null, `gdpr_compliance` tinyint(1) not null default '0', `is_active` tinyint(1) not null default '1', `views` int not null default '0', `interactions` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') {"exception":"[object] (Illuminate\\Database\\QueryException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1101 BLOB, TEXT, GEOMETRY or JSON column 'welcome_message' can't have a default value (Connection: mysql, SQL: create table `widgets` (`id` bigint unsigned not null auto_increment primary key, `user_id` bigint unsigned not null, `name` varchar(255) not null, `slug` varchar(255) not null, `primary_color` varchar(255) not null default '#3b82f6', `secondary_color` varchar(255) not null default '#64748b', `background_color` varchar(255) not null default '#ffffff', `text_color` varchar(255) not null default '#1f2937', `border_color` varchar(255) not null default '#e5e7eb', `font_family` varchar(255) not null default 'inter', `font_size` varchar(255) not null default 'medium', `border_radius` varchar(255) not null default 'medium', `border_width` varchar(255) not null default '1', `shadow_size` varchar(255) not null default 'medium', `position` varchar(255) not null default 'bottom-right', `widget_size` varchar(255) not null default 'medium', `margin_x` int not null default '20', `margin_y` int not null default '20', `auto_open` tinyint(1) not null default '0', `show_on_mobile` tinyint(1) not null default '1', `allow_minimize` tinyint(1) not null default '1', `auto_open_delay` int not null default '3', `sound_notifications` tinyint(1) not null default '0', `typing_indicator` tinyint(1) not null default '1', `message_timestamps` tinyint(1) not null default '1', `response_delay` int not null default '1', `z_index` int not null default '9999', `animation_style` varchar(255) not null default 'slide', `welcome_title` varchar(255) not null default 'Hi there! 👋', `welcome_message` text not null default 'How can I help you today?', `welcome_delay` int not null default '2', `input_placeholder` varchar(255) not null default 'Type your message here...', `max_message_length` int not null default '500', `chat_button_text` varchar(255) not null default 'Start Chat', `send_button_text` varchar(255) not null default 'Send', `close_button_text` varchar(255) not null default 'Close Chat', `minimize_button_text` varchar(255) not null default 'Minimize', `quick_replies` json null, `connection_error` varchar(255) not null default 'Unable to connect. Please check your internet connection.', `typing_message` varchar(255) not null default 'Assistant is typing...', `offline_message` text not null default 'We are currently offline. Please leave a message and we will get back to you soon.', `company_name` varchar(255) not null default 'Acme Corp', `company_logo` varchar(255) null, `agent_name` varchar(255) not null default 'AI Assistant', `agent_avatar` varchar(255) null, `show_powered_by` tinyint(1) not null default '1', `powered_by_text` varchar(255) not null default 'Powered by ChatWidget Builder', `logo_url` varchar(255) null, `privacy_policy_url` varchar(255) null, `terms_of_service_url` varchar(255) null, `gdpr_compliance` tinyint(1) not null default '0', `is_active` tinyint(1) not null default '1', `views` int not null default '0', `interactions` int not null default '0', `created_at` timestamp null, `updated_at` timestamp null) default character set utf8mb4 collate 'utf8mb4_unicode_ci') at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `w...', Array, Object(Closure))
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `w...', Array, Object(Closure))
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `w...')
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('widgets', Object(Closure))
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\database\\migrations\\2024_08_01_000001_create_widgets_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2024_08_01_0000...', Object(Closure))
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_08_01_0000...', Object(Closure))
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\laragon\\\\www\\\\...', 1, false)
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#30 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call('migrate', Array)
#31 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#32 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#33 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#34 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#35 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#36 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#37 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#38 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#39 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#45 {main}

[previous exception] [object] (PDOException(code: 42000): SQLSTATE[42000]: Syntax error or access violation: 1101 BLOB, TEXT, GEOMETRY or JSON column 'welcome_message' can't have a default value at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('create table `w...')
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('create table `w...', Array)
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('create table `w...', Array, Object(Closure))
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('create table `w...', Array, Object(Closure))
#4 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('create table `w...')
#5 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('widgets', Object(Closure))
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\database\\migrations\\2024_08_01_000001_create_widgets_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2024_08_01_0000...', Object(Closure))
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_08_01_0000...', Object(Closure))
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\laragon\\\\www\\\\...', 1, false)
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(68): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#31 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(29): Illuminate\\Console\\Command->runCommand('migrate', Array, Object(Illuminate\\Console\\OutputStyle))
#32 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(81): Illuminate\\Console\\Command->call('migrate', Array)
#33 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#34 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#35 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#36 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#37 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#38 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#39 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#40 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#41 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#43 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#44 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#45 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#46 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#47 {main}
"} 
[2025-06-12 21:46:30] local.ERROR: could not find driver (Connection: sqlite, SQL: select exists (select 1 from "main".sqlite_master where name = 'migrations' and type = 'table') as "exists") {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: sqlite, SQL: select exists (select 1 from \"main\".sqlite_master where name = 'migrations' and type = 'table') as \"exists\") at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#4 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#5 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#29 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('sqlite:C:\\\\larag...', NULL, Object(SensitiveParameterValue), Array)
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('sqlite:C:\\\\larag...', NULL, Object(SensitiveParameterValue), Array)
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(21): Illuminate\\Database\\Connectors\\Connector->createConnection('sqlite:C:\\\\larag...', Array, Array)
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(223): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#29 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#30 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#31 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#32 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#39 {main}
"} 
[2025-06-12 21:46:40] local.ERROR: could not find driver (Connection: sqlite, SQL: select exists (select 1 from "main".sqlite_master where name = 'migrations' and type = 'table') as "exists") {"exception":"[object] (Illuminate\\Database\\QueryException(code: 0): could not find driver (Connection: sqlite, SQL: select exists (select 1 from \"main\".sqlite_master where name = 'migrations' and type = 'table') as \"exists\") at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#4 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#5 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#29 {main}

[previous exception] [object] (PDOException(code: 0): could not find driver at C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php:66)
[stacktrace]
#0 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(66): PDO->__construct('sqlite:C:\\\\larag...', NULL, Object(SensitiveParameterValue), Array)
#1 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('sqlite:C:\\\\larag...', NULL, Object(SensitiveParameterValue), Array)
#2 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\SQLiteConnector.php(21): Illuminate\\Database\\Connectors\\Connector->createConnection('sqlite:C:\\\\larag...', Array, Array)
#3 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connectors\\ConnectionFactory.php(223): Illuminate\\Database\\Connectors\\SQLiteConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1228): call_user_func(Object(Closure))
#6 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(1264): Illuminate\\Database\\Connection->getPdo()
#7 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(509): Illuminate\\Database\\Connection->getReadPdo()
#8 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(true)
#9 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select exists (...', Array)
#10 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select exists (...', Array, Object(Closure))
#11 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(395): Illuminate\\Database\\Connection->run('select exists (...', Array, Object(Closure))
#12 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(341): Illuminate\\Database\\Connection->select('select exists (...', Array, true)
#13 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(358): Illuminate\\Database\\Connection->selectOne('select exists (...', Array, true)
#14 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(174): Illuminate\\Database\\Connection->scalar('select exists (...')
#15 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\DatabaseMigrationRepository.php(183): Illuminate\\Database\\Schema\\Builder->hasTable('migrations')
#16 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(749): Illuminate\\Database\\Migrations\\DatabaseMigrationRepository->repositoryExists()
#17 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): Illuminate\\Database\\Migrations\\Migrator->repositoryExists()
#18 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(338): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}(1)
#19 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(164): retry(1, Object(Closure), 0, Object(Closure))
#20 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(140): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->repositoryExists()
#21 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(110): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->prepareDatabase()
#22 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#23 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#24 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#25 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#26 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#27 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#28 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#29 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#30 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#31 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#32 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\laragon\\www\\temp\\vo\\cursor-test\\laravel12-api\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#39 {main}
"} 
