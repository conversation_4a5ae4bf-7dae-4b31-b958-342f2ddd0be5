"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Plus, Settings, Check, AlertCircle, Zap, Bot } from "lucide-react"
import { ProviderCard } from "@/components/ai-providers/provider-card"
import { ProviderConfig } from "@/components/ai-providers/provider-config"
import { ProviderTestPanel } from "@/components/ai-providers/provider-test-panel"

// Mock data for AI providers
const aiProviders = [
    {
        id: "openai",
        name: "OpenAI",
        description: "GPT-4, GPT-3.5 Turbo, and other OpenAI models",
        logo: "🤖",
        status: "connected" as const,
        models: ["gpt-4", "gpt-3.5-turbo", "gpt-4-turbo"],
        pricing: "Pay per token",
        features: ["Chat Completion", "Function Calling", "Streaming"],
        isPopular: true
    },
    {
        id: "anthropic",
        name: "Anthropic",
        description: "Claude 3 Opus, Sonnet, and Haiku models",
        logo: "🧠",
        status: "disconnected" as const,
        models: ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku"],
        pricing: "Pay per token",
        features: ["Chat Completion", "Long Context", "Safety Focused"],
        isPopular: true
    },
    {
        id: "google",
        name: "Google AI",
        description: "Gemini Pro and other Google AI models",
        logo: "🔍",
        status: "disconnected" as const,
        models: ["gemini-pro", "gemini-pro-vision"],
        pricing: "Pay per token",
        features: ["Chat Completion", "Vision", "Multimodal"],
        isPopular: false
    },
    {
        id: "azure",
        name: "Azure OpenAI",
        description: "OpenAI models hosted on Microsoft Azure",
        logo: "☁️",
        status: "disconnected" as const,
        models: ["gpt-4", "gpt-3.5-turbo"],
        pricing: "Enterprise pricing",
        features: ["Enterprise Security", "Data Residency", "SLA"],
        isPopular: false
    },
    {
        id: "cohere",
        name: "Cohere",
        description: "Command and other Cohere language models",
        logo: "🎯",
        status: "disconnected" as const,
        models: ["command", "command-light"],
        pricing: "Pay per token",
        features: ["Chat Completion", "Embeddings", "Classification"],
        isPopular: false
    },
    {
        id: "huggingface",
        name: "Hugging Face",
        description: "Open source models via Hugging Face Inference API",
        logo: "🤗",
        status: "disconnected" as const,
        models: ["llama-2", "mistral-7b", "code-llama"],
        pricing: "Free tier available",
        features: ["Open Source", "Custom Models", "Inference API"],
        isPopular: false
    }
]

export function AIProvidersLayout() {
    const [selectedProvider, setSelectedProvider] = useState<string | null>(null)
    const [activeTab, setActiveTab] = useState("overview")

    const connectedProviders = aiProviders.filter(p => p.status === "connected")
    const availableProviders = aiProviders.filter(p => p.status === "disconnected")

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
            {/* Hero Header Section */}
            <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white">
                <div className="absolute inset-0 bg-black/20"></div>
                <div className="absolute inset-0 opacity-30" style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                }}></div>
                <div className="relative px-6 py-16">
                    <div className="w-full">
                        <div className="flex items-center gap-4 mb-4">
                            <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                                <Bot className="h-8 w-8" />
                            </div>
                            <div>
                                <h1 className="text-4xl font-bold mb-2">AI Providers</h1>
                                <p className="text-blue-100 text-lg">
                                    Connect and manage your AI language model providers
                                </p>
                            </div>
                        </div>

                        {/* Quick Stats */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
                            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold">{connectedProviders.length}</div>
                                <div className="text-sm text-blue-100">Connected</div>
                            </div>
                            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold">
                                    {connectedProviders.reduce((acc, p) => acc + p.models.length, 0)}
                                </div>
                                <div className="text-sm text-blue-100">Models</div>
                            </div>
                            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold">$24.50</div>
                                <div className="text-sm text-blue-100">Monthly Usage</div>
                            </div>
                            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold">99.9%</div>
                                <div className="text-sm text-blue-100">Uptime</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="w-full px-6 py-8">
                <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-8">
                    {/* Enhanced Tab Navigation */}
                    <div className="bg-white dark:bg-slate-900 rounded-2xl shadow-lg border border-slate-200 dark:border-slate-700 p-1">
                        <TabsList className="grid w-full grid-cols-3 bg-transparent gap-1 h-auto p-1">
                            <TabsTrigger
                                value="overview"
                                className="flex items-center justify-center gap-2 rounded-lg py-2.5 px-3 text-sm font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white data-[state=active]:hover:from-blue-600 data-[state=active]:hover:to-purple-600"
                            >
                                <Check className="h-4 w-4" />
                                <span>Overview</span>
                            </TabsTrigger>
                            <TabsTrigger
                                value="configure"
                                className="flex items-center justify-center gap-2 rounded-lg py-2.5 px-3 text-sm font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white data-[state=active]:hover:from-blue-600 data-[state=active]:hover:to-purple-600"
                            >
                                <Settings className="h-4 w-4" />
                                <span>Configure</span>
                            </TabsTrigger>
                            <TabsTrigger
                                value="test"
                                className="flex items-center justify-center gap-2 rounded-lg py-2.5 px-3 text-sm font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white data-[state=active]:hover:from-blue-600 data-[state=active]:hover:to-purple-600"
                            >
                                <Zap className="h-4 w-4" />
                                <span>Test & Monitor</span>
                            </TabsTrigger>
                        </TabsList>
                    </div>

                    <TabsContent value="overview" className="space-y-8">
                        {/* Stats Cards */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-green-50 dark:from-slate-900 dark:to-slate-800">
                                <CardHeader className="bg-gradient-to-r from-green-500 to-emerald-500 text-white pb-4 pt-6 rounded-t-lg">
                                    <div className="flex items-center justify-between">
                                        <CardTitle className="text-sm font-medium">Connected Providers</CardTitle>
                                        <Check className="h-4 w-4" />
                                    </div>
                                </CardHeader>
                                <CardContent className="p-6">
                                    <div className="text-2xl font-bold">{connectedProviders.length}</div>
                                    <p className="text-xs text-muted-foreground">
                                        Active integrations
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-blue-50 dark:from-slate-900 dark:to-slate-800">
                                <CardHeader className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white pb-4 pt-6 rounded-t-lg">
                                    <div className="flex items-center justify-between">
                                        <CardTitle className="text-sm font-medium">Available Models</CardTitle>
                                        <Zap className="h-4 w-4" />
                                    </div>
                                </CardHeader>
                                <CardContent className="p-6">
                                    <div className="text-2xl font-bold">
                                        {connectedProviders.reduce((acc, p) => acc + p.models.length, 0)}
                                    </div>
                                    <p className="text-xs text-muted-foreground">
                                        Ready to use
                                    </p>
                                </CardContent>
                            </Card>

                            <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-orange-50 dark:from-slate-900 dark:to-slate-800">
                                <CardHeader className="bg-gradient-to-r from-orange-500 to-red-500 text-white pb-4 pt-6 rounded-t-lg">
                                    <div className="flex items-center justify-between">
                                        <CardTitle className="text-sm font-medium">Monthly Usage</CardTitle>
                                        <AlertCircle className="h-4 w-4" />
                                    </div>
                                </CardHeader>
                                <CardContent className="p-6">
                                    <div className="text-2xl font-bold">$24.50</div>
                                    <p className="text-xs text-muted-foreground">
                                        Current billing cycle
                                    </p>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Connected Providers */}
                        {connectedProviders.length > 0 && (
                            <div className="space-y-6">
                                <div className="flex items-center justify-between">
                                    <h2 className="text-2xl font-semibold text-slate-800 dark:text-slate-200">Connected Providers</h2>
                                    <Badge variant="secondary" className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 px-3 py-1">
                                        {connectedProviders.length} Active
                                    </Badge>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    {connectedProviders.map((provider) => (
                                        <ProviderCard
                                            key={provider.id}
                                            provider={provider}
                                            onConfigure={() => {
                                                setSelectedProvider(provider.id)
                                                setActiveTab("configure")
                                            }}
                                        />
                                    ))}
                                </div>
                            </div>
                        )}

                        {/* Available Providers */}
                        <div className="space-y-6">
                            <div className="flex items-center justify-between">
                                <h2 className="text-2xl font-semibold text-slate-800 dark:text-slate-200">Available Providers</h2>
                                <Button variant="outline" size="sm" className="border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800">
                                    <Plus className="h-4 w-4 mr-2" />
                                    Add Custom Provider
                                </Button>
                            </div>

                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                {availableProviders.map((provider) => (
                                    <ProviderCard
                                        key={provider.id}
                                        provider={provider}
                                        onConfigure={() => {
                                            setSelectedProvider(provider.id)
                                            setActiveTab("configure")
                                        }}
                                    />
                                ))}
                            </div>
                        </div>
                    </TabsContent>

                    <TabsContent value="configure" className="space-y-6">
                        {selectedProvider ? (
                            <ProviderConfig
                                provider={aiProviders.find(p => p.id === selectedProvider)!}
                                onBack={() => setActiveTab("overview")}
                            />
                        ) : (
                            <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-blue-50 dark:from-slate-900 dark:to-slate-800">
                                <CardHeader className="bg-gradient-to-r from-blue-500 to-purple-500 text-white pb-4 pt-6 rounded-t-lg">
                                    <CardTitle>Select a Provider to Configure</CardTitle>
                                    <CardDescription className="text-blue-100">
                                        Choose an AI provider from the overview tab to configure its settings
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="p-6">
                                    <Button
                                        onClick={() => setActiveTab("overview")}
                                        className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
                                    >
                                        Go to Overview
                                    </Button>
                                </CardContent>
                            </Card>
                        )}
                    </TabsContent>

                    <TabsContent value="test" className="space-y-6">
                        <ProviderTestPanel providers={connectedProviders} />
                    </TabsContent>
                </Tabs>
            </div>
        </div>
    )
} 