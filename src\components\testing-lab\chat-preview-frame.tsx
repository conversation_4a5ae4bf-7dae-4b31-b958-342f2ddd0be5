"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { MessageCircle } from "lucide-react"
import { SharedWidgetPreview } from "@/components/widget/shared-widget-preview"
import { DeviceMode } from "@/app/testing-lab/page"

interface ChatPreviewFrameProps {
    deviceMode: DeviceMode
    children: React.ReactNode
}

export function ChatPreviewFrame({ deviceMode, children }: ChatPreviewFrameProps) {
    const [widgetOpen, setWidgetOpen] = useState(true)

    const getFrameClasses = () => {
        switch (deviceMode) {
            case "desktop":
                return "w-full h-[600px] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900"
            case "tablet":
                return "w-[768px] h-[600px] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 mx-auto"
            case "mobile":
                return "w-[375px] h-[600px] bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900 mx-auto"
        }
    }

    const MockWebsite = () => (
        <div className="h-full relative overflow-hidden rounded-xl">
            {deviceMode === "desktop" ? (
                // Desktop Browser Frame
                <div className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl border border-slate-200 dark:border-slate-700 h-full flex flex-col">
                    <div className="flex items-center gap-3 p-4 border-b border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800 rounded-t-xl flex-shrink-0">
                        <div className="flex gap-2">
                            <div className="w-3 h-3 rounded-full bg-red-500 shadow-sm"></div>
                            <div className="w-3 h-3 rounded-full bg-yellow-500 shadow-sm"></div>
                            <div className="w-3 h-3 rounded-full bg-green-500 shadow-sm"></div>
                        </div>
                        <div className="flex-1 text-center">
                            <div className="bg-white dark:bg-slate-700 rounded-lg px-4 py-1.5 text-sm text-slate-600 dark:text-slate-300 border border-slate-200 dark:border-slate-600 max-w-md mx-auto">
                                🔒 demo-website.com/testing-lab
                            </div>
                        </div>
                    </div>
                    <div className="flex-1 p-8 flex items-center justify-center bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 relative">
                        <div className="text-center">
                            <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-3xl flex items-center justify-center mb-6 mx-auto shadow-xl">
                                <MessageCircle className="h-10 w-10 text-white" />
                            </div>
                            <h3 className="text-2xl font-bold text-slate-800 dark:text-slate-200 mb-3">Testing Environment</h3>
                            <p className="text-slate-500 dark:text-slate-400 max-w-md">
                                This simulates how your chat widget appears on a real website. Test different scenarios and user interactions.
                            </p>
                        </div>
                    </div>
                </div>
            ) : deviceMode === "tablet" ? (
                // Tablet Frame
                <div className="w-full h-full bg-black rounded-[2rem] p-3 shadow-2xl">
                    <div className="w-full h-full bg-white dark:bg-slate-900 rounded-[1.5rem] overflow-hidden flex flex-col">
                        <div className="bg-slate-100 dark:bg-slate-800 px-6 py-3 flex justify-between items-center text-sm flex-shrink-0">
                            <span className="font-semibold text-slate-800 dark:text-slate-200">9:41</span>
                            <div className="flex items-center gap-3">
                                <div className="flex gap-1">
                                    <div className="w-1 h-1 bg-slate-800 dark:bg-slate-200 rounded-full"></div>
                                    <div className="w-1 h-1 bg-slate-800 dark:bg-slate-200 rounded-full"></div>
                                    <div className="w-1 h-1 bg-slate-800 dark:bg-slate-200 rounded-full"></div>
                                </div>
                                <div className="w-8 h-4 border-2 border-slate-800 dark:border-slate-200 rounded-sm">
                                    <div className="w-6 h-2 bg-green-500 rounded-sm m-0.5"></div>
                                </div>
                            </div>
                        </div>
                        <div className="flex-1 p-8 flex items-center justify-center bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
                            <div className="text-center">
                                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-4 mx-auto shadow-lg">
                                    <MessageCircle className="h-8 w-8 text-white" />
                                </div>
                                <h3 className="text-xl font-bold text-slate-800 dark:text-slate-200 mb-2">Tablet View</h3>
                                <p className="text-sm text-slate-500 dark:text-slate-400">
                                    Testing tablet experience
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            ) : (
                // Mobile Frame
                <div className="w-full h-full bg-black rounded-[2.5rem] p-2 shadow-2xl">
                    <div className="w-full h-full bg-white dark:bg-slate-900 rounded-[2rem] overflow-hidden flex flex-col">
                        <div className="bg-slate-100 dark:bg-slate-800 px-6 py-3 flex justify-between items-center text-sm flex-shrink-0">
                            <span className="font-semibold text-slate-800 dark:text-slate-200">9:41</span>
                            <div className="flex items-center gap-2">
                                <div className="flex gap-1">
                                    <div className="w-1 h-1 bg-slate-800 dark:bg-slate-200 rounded-full"></div>
                                    <div className="w-1 h-1 bg-slate-800 dark:bg-slate-200 rounded-full"></div>
                                    <div className="w-1 h-1 bg-slate-800 dark:bg-slate-200 rounded-full"></div>
                                </div>
                                <div className="w-6 h-3 border-2 border-slate-800 dark:border-slate-200 rounded-sm">
                                    <div className="w-4 h-1.5 bg-green-500 rounded-sm m-0.5"></div>
                                </div>
                            </div>
                        </div>
                        <div className="flex-1 p-6 flex items-center justify-center bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
                            <div className="text-center">
                                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-3 mx-auto shadow-lg">
                                    <MessageCircle className="h-6 w-6 text-white" />
                                </div>
                                <h3 className="text-lg font-bold text-slate-800 dark:text-slate-200 mb-2">Mobile View</h3>
                                <p className="text-sm text-slate-500 dark:text-slate-400">
                                    Mobile experience test
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            )}

            {/* Chat Widget */}
            {widgetOpen && (
                <div className={`absolute ${deviceMode === "desktop" ? "bottom-4 right-4" :
                    deviceMode === "tablet" ? "bottom-4 right-4" :
                        "bottom-3 right-3"
                    } z-20 transform transition-all duration-300`}>
                    <SharedWidgetPreview
                        isMobile={deviceMode === "mobile"}
                        onClose={() => setWidgetOpen(false)}
                        showCloseButton={true}
                    />
                </div>
            )}

            {/* Chat Button (when widget is closed) */}
            {!widgetOpen && (
                <div className={`absolute ${deviceMode === "desktop" ? "bottom-4 right-4" :
                    deviceMode === "tablet" ? "bottom-4 right-4" :
                        "bottom-3 right-3"
                    } z-20`}>
                    <Button
                        className="rounded-full w-16 h-16 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 shadow-2xl border-4 border-white dark:border-slate-800 transform transition-all duration-300 hover:scale-110 group"
                        onClick={() => setWidgetOpen(true)}
                    >
                        <MessageCircle className="h-7 w-7 text-white group-hover:scale-110 transition-transform" />
                        <div className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center">
                            <span className="text-xs font-bold text-white">3</span>
                        </div>
                    </Button>
                </div>
            )}

            {/* Mock Chat Messages Overlay */}
            <div className="absolute inset-0 pointer-events-none">
                {children}
            </div>
        </div>
    )

    return (
        <div className="w-full h-full flex items-center justify-center p-4">
            <div className={getFrameClasses()}>
                <MockWebsite />
            </div>
        </div>
    )
} 