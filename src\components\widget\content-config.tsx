"use client"

import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { But<PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { X, Plus } from "lucide-react"
import { WidgetConfig } from "@/hooks/use-widget-config"

interface ContentConfigProps {
    config: WidgetConfig
    updateConfig: (updates: Partial<WidgetConfig>) => void
    updateQuickReply: (index: number, value: string) => void
    addQuickReply: (value: string) => void
    removeQuickReply: (index: number) => void
}

export function ContentConfig({
    config,
    updateConfig,
    updateQuickReply,
    addQuickReply,
    removeQuickReply
}: ContentConfigProps) {
    return (
        <div className="space-y-6">
            <Accordion
                type="multiple"
                defaultValue={["welcome", "buttons", "quickreplies", "branding", "compliance"]}
                className="space-y-4"
            >
                {/* Welcome Messages Section */}
                <AccordionItem
                    value="welcome"
                    className="bg-white dark:bg-slate-900 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden"
                >
                    <AccordionTrigger className="px-6 py-4 text-lg font-semibold hover:no-underline hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                        Welcome Messages
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-6">
                            <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                Configure the initial messages and greetings that users see when they open the widget
                            </p>

                            {/* Welcome Title */}
                            <div className="space-y-3">
                                <Label htmlFor="welcome-title" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                    Welcome Title
                                </Label>
                                <Input
                                    id="welcome-title"
                                    value={config.welcomeTitle}
                                    onChange={(e) => updateConfig({ welcomeTitle: e.target.value })}
                                    placeholder="Welcome to our chat!"
                                    className="h-10"
                                />
                                <p className="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">
                                    The main heading displayed when the chat widget opens
                                </p>
                            </div>

                            {/* Welcome Message */}
                            <div className="space-y-3">
                                <Label htmlFor="welcome-message" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                    Welcome Message
                                </Label>
                                <Textarea
                                    id="welcome-message"
                                    value={config.welcomeMessage}
                                    onChange={(e) => updateConfig({ welcomeMessage: e.target.value })}
                                    placeholder="How can we help you today?"
                                    rows={3}
                                    className="resize-none"
                                />
                                <p className="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">
                                    The greeting message shown to users when they start a conversation
                                </p>
                            </div>

                            {/* Welcome Delay and Offline Message */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-3">
                                    <Label htmlFor="welcome-delay" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Welcome Delay
                                    </Label>
                                    <div className="flex items-center gap-3">
                                        <Input
                                            id="welcome-delay"
                                            type="number"
                                            value={config.welcomeDelay}
                                            onChange={(e) => updateConfig({ welcomeDelay: parseInt(e.target.value) || 0 })}
                                            min="0"
                                            max="10"
                                            className="flex-1 h-10"
                                        />
                                        <span className="text-sm text-slate-500 dark:text-slate-400 font-medium">seconds</span>
                                    </div>
                                    <p className="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">
                                        Delay before showing the welcome message
                                    </p>
                                </div>
                            </div>

                            {/* Offline Message */}
                            <div className="space-y-3">
                                <Label htmlFor="offline-message" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                    Offline Message
                                </Label>
                                <Textarea
                                    id="offline-message"
                                    value={config.offlineMessage}
                                    onChange={(e) => updateConfig({ offlineMessage: e.target.value })}
                                    placeholder="We're currently offline. Please leave a message and we'll get back to you."
                                    rows={3}
                                    className="resize-none"
                                />
                                <p className="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">
                                    Message displayed when the chat service is offline or unavailable
                                </p>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Button Configuration Section */}
                <AccordionItem
                    value="buttons"
                    className="bg-white dark:bg-slate-900 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden"
                >
                    <AccordionTrigger className="px-6 py-4 text-lg font-semibold hover:no-underline hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                        Button Configuration
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-6">
                            <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                Customize the text and behavior of all interactive buttons in the widget
                            </p>

                            {/* Button Text Fields */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-3">
                                    <Label htmlFor="chat-button-text" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Chat Button Text
                                    </Label>
                                    <Input
                                        id="chat-button-text"
                                        value={config.chatButtonText}
                                        onChange={(e) => updateConfig({ chatButtonText: e.target.value })}
                                        placeholder="Start Chat"
                                        className="h-10"
                                    />
                                </div>

                                <div className="space-y-3">
                                    <Label htmlFor="send-button-text" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Send Button Text
                                    </Label>
                                    <Input
                                        id="send-button-text"
                                        value={config.sendButtonText}
                                        onChange={(e) => updateConfig({ sendButtonText: e.target.value })}
                                        placeholder="Send"
                                        className="h-10"
                                    />
                                </div>

                                <div className="space-y-3">
                                    <Label htmlFor="close-button-text" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Close Button Text
                                    </Label>
                                    <Input
                                        id="close-button-text"
                                        value={config.closeButtonText}
                                        onChange={(e) => updateConfig({ closeButtonText: e.target.value })}
                                        placeholder="Close Chat"
                                        className="h-10"
                                    />
                                </div>

                                <div className="space-y-3">
                                    <Label htmlFor="minimize-button-text" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Minimize Button Text
                                    </Label>
                                    <Input
                                        id="minimize-button-text"
                                        value={config.minimizeButtonText}
                                        onChange={(e) => updateConfig({ minimizeButtonText: e.target.value })}
                                        placeholder="Minimize"
                                        className="h-10"
                                    />
                                </div>
                            </div>

                            {/* Input Configuration */}
                            <div className="space-y-6 pt-4 border-t border-slate-200 dark:border-slate-700">
                                <div className="space-y-3">
                                    <Label htmlFor="input-placeholder" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Input Placeholder
                                    </Label>
                                    <Input
                                        id="input-placeholder"
                                        value={config.inputPlaceholder}
                                        onChange={(e) => updateConfig({ inputPlaceholder: e.target.value })}
                                        placeholder="Type your message..."
                                        maxLength={100}
                                        className="h-10"
                                    />
                                    <p className="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">
                                        Placeholder text shown in the message input field
                                    </p>
                                </div>

                                <div className="space-y-3">
                                    <Label htmlFor="max-message-length" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Max Message Length
                                    </Label>
                                    <div className="flex items-center gap-3">
                                        <Input
                                            id="max-message-length"
                                            type="number"
                                            value={config.maxMessageLength}
                                            onChange={(e) => updateConfig({ maxMessageLength: parseInt(e.target.value) || 500 })}
                                            min="50"
                                            max="2000"
                                            className="flex-1 h-10"
                                        />
                                        <span className="text-sm text-slate-500 dark:text-slate-400 font-medium">characters</span>
                                    </div>
                                    <p className="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">
                                        Maximum number of characters users can type in a single message (50-2000)
                                    </p>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Quick Replies Section */}
                <AccordionItem
                    value="quickreplies"
                    className="bg-white dark:bg-slate-900 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden"
                >
                    <AccordionTrigger className="px-6 py-4 text-lg font-semibold hover:no-underline hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                        Quick Replies
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-6">
                            <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                Add predefined responses for common questions to help users get started quickly
                            </p>

                            {/* Quick Reply List */}
                            <div className="space-y-4">
                                {config.quickReplies.map((reply, index) => (
                                    <div key={index} className="flex gap-3 items-center p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                                        <div className="flex-1">
                                            <Input
                                                value={reply}
                                                onChange={(e) => updateQuickReply(index, e.target.value)}
                                                placeholder={`Quick reply ${index + 1}`}
                                                className="h-10 bg-white dark:bg-slate-900"
                                            />
                                        </div>
                                        <Button
                                            variant="outline"
                                            size="icon"
                                            onClick={() => removeQuickReply(index)}
                                            className="h-10 w-10 flex-shrink-0 hover:bg-red-50 hover:border-red-200 hover:text-red-600 dark:hover:bg-red-950 dark:hover:border-red-800"
                                        >
                                            <X className="h-4 w-4" />
                                        </Button>
                                    </div>
                                ))}

                                {/* Add Quick Reply Button */}
                                <Button
                                    variant="outline"
                                    onClick={() => addQuickReply("")}
                                    className="w-full h-12 border-2 border-dashed border-slate-300 dark:border-slate-600 hover:border-blue-400 hover:bg-blue-50 dark:hover:bg-blue-950 transition-colors"
                                    disabled={config.quickReplies.length >= 6}
                                >
                                    <Plus className="h-4 w-4 mr-2" />
                                    Add Quick Reply {config.quickReplies.length < 6 && `(${config.quickReplies.length}/6)`}
                                </Button>

                                {config.quickReplies.length >= 6 && (
                                    <p className="text-xs text-slate-500 dark:text-slate-400 text-center">
                                        Maximum of 6 quick replies allowed
                                    </p>
                                )}
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Branding Section */}
                <AccordionItem
                    value="branding"
                    className="bg-white dark:bg-slate-900 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden"
                >
                    <AccordionTrigger className="px-6 py-4 text-lg font-semibold hover:no-underline hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                        Branding &amp; Identity
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-6">
                            <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                Configure your company branding and identity elements within the widget
                            </p>

                            {/* Company Information */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-3">
                                    <Label htmlFor="company-name" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Company Name
                                    </Label>
                                    <Input
                                        id="company-name"
                                        value={config.companyName}
                                        onChange={(e) => updateConfig({ companyName: e.target.value })}
                                        placeholder="Your Company Name"
                                        className="h-10"
                                    />
                                </div>

                                <div className="space-y-3">
                                    <Label htmlFor="company-logo" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Company Logo URL
                                    </Label>
                                    <Input
                                        id="company-logo"
                                        value={config.companyLogo}
                                        onChange={(e) => updateConfig({ companyLogo: e.target.value })}
                                        placeholder="https://example.com/logo.png"
                                        className="h-10"
                                    />
                                </div>
                            </div>

                            {/* Powered By Settings */}
                            <div className="space-y-4 pt-4 border-t border-slate-200 dark:border-slate-700">
                                <div className="flex items-start justify-between gap-4 p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                                    <div className="space-y-1 flex-1">
                                        <Label htmlFor="show-powered-by" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                            Show "Powered By" Branding
                                        </Label>
                                        <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                            Display attribution link in the widget footer
                                        </p>
                                    </div>
                                    <Switch
                                        id="show-powered-by"
                                        checked={config.showPoweredBy}
                                        onCheckedChange={(checked) => updateConfig({ showPoweredBy: checked })}
                                    />
                                </div>

                                <div className="space-y-3">
                                    <Label htmlFor="powered-by-text" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Powered By Text
                                    </Label>
                                    <Input
                                        id="powered-by-text"
                                        value={config.poweredByText}
                                        onChange={(e) => updateConfig({ poweredByText: e.target.value })}
                                        placeholder="Powered by ChatWidget"
                                        className="h-10"
                                        disabled={!config.showPoweredBy}
                                    />
                                    <p className="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">
                                        Custom text for the powered by attribution
                                    </p>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Compliance Section */}
                <AccordionItem
                    value="compliance"
                    className="bg-white dark:bg-slate-900 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden"
                >
                    <AccordionTrigger className="px-6 py-4 text-lg font-semibold hover:no-underline hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                        Compliance &amp; Legal
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-6">
                            <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                Configure privacy, terms, and compliance-related settings for your widget
                            </p>

                            {/* Privacy Settings */}
                            <div className="space-y-6">
                                <div className="flex items-start justify-between gap-4 p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                                    <div className="space-y-1 flex-1">
                                        <Label htmlFor="gdpr-compliance" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                            GDPR Compliance Mode
                                        </Label>
                                        <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                            Enable GDPR-compliant data handling and consent management
                                        </p>
                                    </div>
                                    <Switch
                                        id="gdpr-compliance"
                                        checked={config.gdprCompliance}
                                        onCheckedChange={(checked) => updateConfig({ gdprCompliance: checked })}
                                    />
                                </div>

                                <div className="space-y-3">
                                    <Label htmlFor="privacy-policy-url" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Privacy Policy URL
                                    </Label>
                                    <Input
                                        id="privacy-policy-url"
                                        value={config.privacyPolicyUrl}
                                        onChange={(e) => updateConfig({ privacyPolicyUrl: e.target.value })}
                                        placeholder="https://example.com/privacy"
                                        className="h-10"
                                    />
                                </div>

                                <div className="space-y-3">
                                    <Label htmlFor="terms-of-service-url" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Terms of Service URL
                                    </Label>
                                    <Input
                                        id="terms-of-service-url"
                                        value={config.termsOfServiceUrl}
                                        onChange={(e) => updateConfig({ termsOfServiceUrl: e.target.value })}
                                        placeholder="https://example.com/terms"
                                        className="h-10"
                                    />
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>
            </Accordion>
        </div>
    )
} 