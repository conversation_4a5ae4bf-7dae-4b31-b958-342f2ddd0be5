"use client"

import { useState, useCallback } from "react"
import { toast } from "sonner"
import { WidgetConfig } from "./use-widget-config"
import { widgetAPI, ApiError, Widget, CreateWidgetRequest, UpdateWidgetRequest } from "@/lib/api"

// Form validation rules matching backend
const VALIDATION_RULES = {
    name: { required: true, maxLength: 100 },
    welcomeTitle: { maxLength: 100 },
    welcomeMessage: { maxLength: 1000 },
    inputPlaceholder: { maxLength: 100 },
    maxMessageLength: { min: 10, max: 2000 },
    chatButtonText: { maxLength: 50 },
    sendButtonText: { maxLength: 50 },
    closeButtonText: { maxLength: 50 },
    minimizeButtonText: { maxLength: 50 },
    connectionError: { maxLength: 200 },
    typingMessage: { maxLength: 100 },
    offlineMessage: { maxLength: 500 },
    companyName: { maxLength: 100 },
    agentName: { maxLength: 100 },
    poweredByText: { maxLength: 100 },
    marginX: { min: 0, max: 100 },
    marginY: { min: 0, max: 100 },
    autoOpenDelay: { min: 0, max: 60 },
    responseDelay: { min: 0, max: 10 },
    welcomeDelay: { min: 0, max: 10 },
    zIndex: { min: 1, max: 99999 },
    quickReplies: { maxItems: 10, itemMaxLength: 100 }
}

export interface FormErrors {
    [key: string]: string[]
}

export interface UseWidgetFormOptions {
    initialData?: Partial<WidgetConfig & { name: string }>
    onSuccess?: (widget: Widget) => void
    onError?: (error: ApiError) => void
}

export interface UseWidgetFormReturn {
    // Form state
    formData: WidgetConfig & { name: string }
    errors: FormErrors
    isLoading: boolean
    isValid: boolean

    // Form actions
    updateField: (field: string, value: any) => void
    updateConfig: (updates: Partial<WidgetConfig>) => void
    validateForm: () => boolean
    clearErrors: () => void
    resetForm: () => void

    // API actions
    createWidget: () => Promise<Widget | null>
    updateWidget: (id: number) => Promise<Widget | null>

    // Validation helpers
    getFieldError: (field: string) => string | undefined
    hasFieldError: (field: string) => boolean
}

export function useWidgetForm(options: UseWidgetFormOptions = {}): UseWidgetFormReturn {
    const { initialData, onSuccess, onError } = options

    // Default form data
    const defaultFormData: WidgetConfig & { name: string } = {
        name: "",
        // Appearance defaults
        primaryColor: "#3b82f6",
        secondaryColor: "#64748b",
        backgroundColor: "#ffffff",
        textColor: "#1f2937",
        borderColor: "#e5e7eb",
        fontFamily: "inter",
        fontSize: "medium",
        borderRadius: "medium",
        borderWidth: "1",
        shadowSize: "medium",
        // Behavior defaults
        position: "bottom-right",
        widgetSize: "medium",
        marginX: 20,
        marginY: 20,
        autoOpen: false,
        showOnMobile: true,
        allowMinimize: true,
        autoOpenDelay: 3,
        soundNotifications: false,
        typingIndicator: true,
        messageTimestamps: true,
        responseDelay: 1,
        zIndex: 9999,
        animationStyle: "slide",
        // Content defaults
        welcomeTitle: "Hi there! 👋",
        welcomeMessage: "How can I help you today?",
        welcomeDelay: 2,
        inputPlaceholder: "Type your message here...",
        maxMessageLength: 500,
        chatButtonText: "Start Chat",
        sendButtonText: "Send",
        closeButtonText: "Close Chat",
        minimizeButtonText: "Minimize",
        quickReplies: [
            "What are your business hours?",
            "How do I get started?",
            "Contact support"
        ],
        connectionError: "Unable to connect. Please check your internet connection.",
        typingMessage: "Assistant is typing...",
        offlineMessage: "We're currently offline. Please leave a message and we'll get back to you soon.",
        companyName: "Acme Corp",
        companyLogo: "",
        agentName: "AI Assistant",
        agentAvatar: "",
        showPoweredBy: true,
        poweredByText: "Powered by ChatWidget Builder",
        logoUrl: "",
        privacyPolicyUrl: "",
        termsOfServiceUrl: "",
        gdprCompliance: false,
        ...initialData
    }

    const [formData, setFormData] = useState(defaultFormData)
    const [errors, setErrors] = useState<FormErrors>({})
    const [isLoading, setIsLoading] = useState(false)

    // Validation function
    const validateField = useCallback((field: string, value: any): string[] => {
        const fieldErrors: string[] = []
        const rule = VALIDATION_RULES[field as keyof typeof VALIDATION_RULES]

        if (!rule) return fieldErrors

        // Required validation
        if (rule.required && (!value || (typeof value === 'string' && value.trim() === ''))) {
            fieldErrors.push(`${field} is required`)
        }

        // String length validations
        if (typeof value === 'string' && value.length > 0) {
            if (rule.maxLength && value.length > rule.maxLength) {
                fieldErrors.push(`${field} must be less than ${rule.maxLength} characters`)
            }
        }

        // Number validations
        if (typeof value === 'number') {
            if (rule.min !== undefined && value < rule.min) {
                fieldErrors.push(`${field} must be at least ${rule.min}`)
            }
            if (rule.max !== undefined && value > rule.max) {
                fieldErrors.push(`${field} must be no more than ${rule.max}`)
            }
        }

        // Array validations (for quickReplies)
        if (Array.isArray(value) && field === 'quickReplies') {
            if (rule.maxItems && value.length > rule.maxItems) {
                fieldErrors.push(`Maximum ${rule.maxItems} quick replies allowed`)
            }
            value.forEach((item, index) => {
                if (typeof item === 'string' && rule.itemMaxLength && item.length > rule.itemMaxLength) {
                    fieldErrors.push(`Quick reply ${index + 1} must be less than ${rule.itemMaxLength} characters`)
                }
            })
        }

        // URL validation
        if (field.includes('Url') && value && typeof value === 'string') {
            try {
                new URL(value)
            } catch {
                fieldErrors.push(`${field} must be a valid URL`)
            }
        }

        return fieldErrors
    }, [])

    const validateForm = useCallback((): boolean => {
        const newErrors: FormErrors = {}

        // Validate all fields
        Object.entries(formData).forEach(([field, value]) => {
            const fieldErrors = validateField(field, value)
            if (fieldErrors.length > 0) {
                newErrors[field] = fieldErrors
            }
        })

        setErrors(newErrors)
        return Object.keys(newErrors).length === 0
    }, [formData, validateField])

    const updateField = useCallback((field: string, value: any) => {
        setFormData(prev => ({ ...prev, [field]: value }))

        // Clear field errors when user starts typing
        if (errors[field]) {
            setErrors(prev => {
                const newErrors = { ...prev }
                delete newErrors[field]
                return newErrors
            })
        }
    }, [errors])

    const updateConfig = useCallback((updates: Partial<WidgetConfig>) => {
        setFormData(prev => ({ ...prev, ...updates }))

        // Clear errors for updated fields
        Object.keys(updates).forEach(field => {
            if (errors[field]) {
                setErrors(prev => {
                    const newErrors = { ...prev }
                    delete newErrors[field]
                    return newErrors
                })
            }
        })
    }, [errors])

    const clearErrors = useCallback(() => {
        setErrors({})
    }, [])

    const resetForm = useCallback(() => {
        setFormData(defaultFormData)
        setErrors({})
    }, [defaultFormData])

    const createWidget = useCallback(async (): Promise<Widget | null> => {
        if (!validateForm()) {
            toast.error("Please fix the validation errors before submitting")
            return null
        }

        setIsLoading(true)

        try {
            const { name, ...config } = formData
            const requestData: CreateWidgetRequest = {
                name,
                ...config
            }

            const widget = await widgetAPI.createWidget(requestData)

            toast.success("Widget created successfully! 🎉", {
                description: `Your widget "${name}" is now ready to use.`
            })

            onSuccess?.(widget)
            return widget

        } catch (error) {
            const apiError = error as ApiError

            if (apiError.errors) {
                setErrors(apiError.errors)
                toast.error("Validation failed", {
                    description: "Please check the highlighted fields and try again."
                })
            } else {
                toast.error("Failed to create widget", {
                    description: apiError.message || "An unexpected error occurred."
                })
            }

            onError?.(apiError)
            return null

        } finally {
            setIsLoading(false)
        }
    }, [formData, validateForm, onSuccess, onError])

    const updateWidget = useCallback(async (id: number): Promise<Widget | null> => {
        if (!validateForm()) {
            toast.error("Please fix the validation errors before submitting")
            return null
        }

        setIsLoading(true)

        try {
            const { name, ...config } = formData
            const requestData: UpdateWidgetRequest = {
                name,
                ...config
            }

            const widget = await widgetAPI.updateWidget(id, requestData)

            toast.success("Widget updated successfully! ✨", {
                description: `Your changes to "${name}" have been saved.`
            })

            onSuccess?.(widget)
            return widget

        } catch (error) {
            const apiError = error as ApiError

            if (apiError.errors) {
                setErrors(apiError.errors)
                toast.error("Validation failed", {
                    description: "Please check the highlighted fields and try again."
                })
            } else {
                toast.error("Failed to update widget", {
                    description: apiError.message || "An unexpected error occurred."
                })
            }

            onError?.(apiError)
            return null

        } finally {
            setIsLoading(false)
        }
    }, [formData, validateForm, onSuccess, onError])

    const getFieldError = useCallback((field: string): string | undefined => {
        return errors[field]?.[0]
    }, [errors])

    const hasFieldError = useCallback((field: string): boolean => {
        return Boolean(errors[field] && errors[field].length > 0)
    }, [errors])

    const isValid = Object.keys(errors).length === 0

    return {
        // Form state
        formData,
        errors,
        isLoading,
        isValid,

        // Form actions
        updateField,
        updateConfig,
        validateForm,
        clearErrors,
        resetForm,

        // API actions
        createWidget,
        updateWidget,

        // Validation helpers
        getFieldError,
        hasFieldError
    }
} 