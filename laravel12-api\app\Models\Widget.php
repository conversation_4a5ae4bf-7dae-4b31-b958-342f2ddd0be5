<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Widget extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        // Appearance
        'primary_color',
        'secondary_color',
        'background_color',
        'text_color',
        'border_color',
        'font_family',
        'font_size',
        'border_radius',
        'border_width',
        'shadow_size',
        // Behavior
        'position',
        'widget_size',
        'margin_x',
        'margin_y',
        'auto_open',
        'show_on_mobile',
        'allow_minimize',
        'auto_open_delay',
        'sound_notifications',
        'typing_indicator',
        'message_timestamps',
        'response_delay',
        'z_index',
        'animation_style',
        // Content
        'welcome_title',
        'welcome_message',
        'welcome_delay',
        'input_placeholder',
        'max_message_length',
        'chat_button_text',
        'send_button_text',
        'close_button_text',
        'minimize_button_text',
        'quick_replies',
        'connection_error',
        'typing_message',
        'offline_message',
        'company_name',
        'company_logo',
        'agent_name',
        'agent_avatar',
        'show_powered_by',
        'powered_by_text',
        'logo_url',
        'privacy_policy_url',
        'terms_of_service_url',
        'gdpr_compliance',
        // Tracking
        'is_active',
        'views',
        'interactions',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'auto_open' => 'boolean',
        'show_on_mobile' => 'boolean',
        'allow_minimize' => 'boolean',
        'sound_notifications' => 'boolean',
        'typing_indicator' => 'boolean',
        'message_timestamps' => 'boolean',
        'show_powered_by' => 'boolean',
        'gdpr_compliance' => 'boolean',
        'is_active' => 'boolean',
        'quick_replies' => 'array',
        'margin_x' => 'integer',
        'margin_y' => 'integer',
        'auto_open_delay' => 'integer',
        'response_delay' => 'integer',
        'welcome_delay' => 'integer',
        'max_message_length' => 'integer',
        'z_index' => 'integer',
        'views' => 'integer',
        'interactions' => 'integer',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'welcome_message' => 'How can I help you today?',
        'offline_message' => 'We are currently offline. Please leave a message and we will get back to you soon.',
    ];

    /**
     * Get the user that owns the widget.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
