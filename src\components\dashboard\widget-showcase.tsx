"use client"

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"
import { <PERSON>ton } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { WidgetConfig } from "@/hooks/use-widget-config"
import { ArrowRight, Star, Copy, Check, ExternalLink, MessageSquare } from "lucide-react"
import { cn } from "@/lib/utils"

interface WidgetShowcaseProps {
  widgets: {
    id: string
    name: string
    description: string
    config: Partial<WidgetConfig>
    stats?: {
      conversations?: number
      messages?: number
      rating?: number
    }
    tags?: string[]
    featured?: boolean
  }[]
  className?: string
  onSelect?: (id: string) => void
}

export function WidgetShowcase({ widgets = [], className = "", onSelect }: WidgetShowcaseProps) {
  const [activeIndex, setActiveIndex] = useState(0)
  const [isHovered, setIsHovered] = useState<string | null>(null)
  const [copied, setCopied] = useState<string | null>(null)
  const [visibleItems, setVisibleItems] = useState<string[]>([])
  
  // Animation on mount - staggered appearance
  useEffect(() => {
    const timer = setTimeout(() => {
      const ids = widgets.map(w => w.id)
      setVisibleItems(ids)
    }, 100)
    
    return () => clearTimeout(timer)
  }, [widgets])
  
  // Handle copy widget ID
  const handleCopy = (id: string, e: React.MouseEvent) => {
    e.stopPropagation()
    setCopied(id)
    setTimeout(() => setCopied(null), 2000)
    // In a real app, this would copy the ID to clipboard
  }
  
  // Get gradient based on primary color
  const getGradient = (color: string = "#3b82f6") => {
    return `linear-gradient(135deg, ${color}, ${adjustColorBrightness(color, -20)})`
  }
  
  // Helper to adjust color brightness
  function adjustColorBrightness(color: string, percent: number): string {
    // Simple implementation for demo purposes
    if (color.startsWith('#')) {
      color = color.slice(1)
    }
    
    const num = parseInt(color, 16)
    const r = (num >> 16) + percent
    const g = ((num >> 8) & 0x00FF) + percent
    const b = (num & 0x0000FF) + percent
    
    const newR = Math.min(255, Math.max(0, r)).toString(16).padStart(2, '0')
    const newG = Math.min(255, Math.max(0, g)).toString(16).padStart(2, '0')
    const newB = Math.min(255, Math.max(0, b)).toString(16).padStart(2, '0')
    
    return `#${newR}${newG}${newB}`
  }
  
  return (
    <div className={cn("space-y-4", className)}>
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-lg font-semibold">Featured Widgets</h3>
        <Button variant="ghost" size="sm" className="gap-1 text-sm">
          View all <ArrowRight className="h-3.5 w-3.5" />
        </Button>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {widgets.map((widget, index) => (
          <Card
            key={widget.id}
            className={cn(
              "overflow-hidden border border-slate-200 dark:border-slate-700 transition-all duration-500 transform",
              visibleItems.includes(widget.id) ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8",
              isHovered === widget.id ? "shadow-lg scale-[1.02]" : "shadow",
              "cursor-pointer"
            )}
            style={{ transitionDelay: `${index * 100}ms` }}
            onMouseEnter={() => setIsHovered(widget.id)}
            onMouseLeave={() => setIsHovered(null)}
            onClick={() => onSelect?.(widget.id)}
          >
            {/* Widget header with gradient background */}
            <div 
              className="p-4 relative overflow-hidden"
              style={{ 
                background: getGradient(widget.config.primaryColor),
                height: "80px"
              }}
            >
              {/* Decorative elements */}
              <div className="absolute top-0 left-0 w-full h-full opacity-20">
                <div className="absolute top-0 right-0 w-40 h-40 bg-white rounded-full blur-3xl -translate-y-1/2 translate-x-1/2"></div>
                <div className="absolute bottom-0 left-0 w-20 h-20 bg-black rounded-full blur-2xl -translate-x-1/2 translate-y-1/2"></div>
              </div>
              
              {/* Widget icon */}
              <div className="absolute bottom-0 right-4 transform translate-y-1/2 bg-white dark:bg-slate-800 rounded-xl p-2 shadow-lg">
                <div 
                  className="w-10 h-10 rounded-lg flex items-center justify-center"
                  style={{ background: widget.config.primaryColor || "#3b82f6" }}
                >
                  <MessageSquare className="h-5 w-5 text-white" />
                </div>
              </div>
              
              {/* Featured badge */}
              {widget.featured && (
                <Badge 
                  variant="secondary" 
                  className="absolute top-2 left-2 bg-white/90 dark:bg-slate-800/90 text-amber-500 gap-1 backdrop-blur-sm"
                >
                  <Star className="h-3 w-3 fill-amber-500" /> Featured
                </Badge>
              )}
            </div>
            
            {/* Widget content */}
            <div className="p-4 pt-6">
              <div className="flex items-start justify-between">
                <div>
                  <h4 className="font-medium text-base">{widget.name}</h4>
                  <p className="text-sm text-slate-500 dark:text-slate-400 mt-1 line-clamp-2">
                    {widget.description}
                  </p>
                </div>
                
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 text-slate-500"
                  onClick={(e) => handleCopy(widget.id, e)}
                >
                  {copied === widget.id ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
              
              {/* Tags */}
              {widget.tags && widget.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-3">
                  {widget.tags.map(tag => (
                    <Badge 
                      key={tag} 
                      variant="outline" 
                      className="text-xs bg-slate-50 dark:bg-slate-800 font-normal"
                    >
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}
              
              {/* Stats */}
              {widget.stats && (
                <div className="grid grid-cols-3 gap-2 mt-4 pt-3 border-t border-slate-100 dark:border-slate-800">
                  {widget.stats.conversations !== undefined && (
                    <div className="text-center">
                      <p className="text-xs text-slate-500 dark:text-slate-400">Conversations</p>
                      <p className="font-medium">{widget.stats.conversations.toLocaleString()}</p>
                    </div>
                  )}
                  
                  {widget.stats.messages !== undefined && (
                    <div className="text-center">
                      <p className="text-xs text-slate-500 dark:text-slate-400">Messages</p>
                      <p className="font-medium">{widget.stats.messages.toLocaleString()}</p>
                    </div>
                  )}
                  
                  {widget.stats.rating !== undefined && (
                    <div className="text-center">
                      <p className="text-xs text-slate-500 dark:text-slate-400">Rating</p>
                      <p className="font-medium flex items-center justify-center gap-1">
                        {widget.stats.rating}
                        <Star className="h-3 w-3 fill-amber-500 text-amber-500" />
                      </p>
                    </div>
                  )}
                </div>
              )}
              
              {/* Hover action */}
              <div 
                className={cn(
                  "mt-4 transition-opacity duration-300 flex items-center justify-between",
                  isHovered === widget.id ? "opacity-100" : "opacity-0"
                )}
              >
                <Button 
                  variant="default" 
                  size="sm" 
                  className="gap-1 text-xs"
                  style={{ 
                    background: widget.config.primaryColor || "#3b82f6",
                    borderColor: adjustColorBrightness(widget.config.primaryColor || "#3b82f6", -10)
                  }}
                >
                  <span>Edit Widget</span> <ArrowRight className="h-3 w-3" />
                </Button>
                
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="gap-1 text-xs"
                >
                  <span>Preview</span> <ExternalLink className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
}
