"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON>, Plug, Star, CheckCircle, AlertCircle, Zap, Shield } from "lucide-react"

interface Provider {
    id: string
    name: string
    description: string
    logo: string
    status: "connected" | "disconnected"
    models: string[]
    pricing: string
    features: string[]
    isPopular: boolean
}

interface ProviderCardProps {
    provider: Provider
    onConfigure: () => void
}

export function ProviderCard({ provider, onConfigure }: ProviderCardProps) {
    const isConnected = provider.status === "connected"

    return (
        <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800 overflow-hidden transition-all duration-200 hover:shadow-xl hover:scale-[1.02]">
            {provider.isPopular && (
                <div className="absolute -top-2 -right-2 z-10">
                    <Badge className="bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg">
                        <Star className="h-3 w-3 mr-1" />
                        Popular
                    </Badge>
                </div>
            )}

            <CardHeader className={`pb-4 pt-6 rounded-t-lg ${isConnected
                ? "bg-gradient-to-r from-green-500 to-emerald-500 text-white"
                : "bg-gradient-to-r from-slate-500 to-slate-600 text-white"
                }`}>
                <div className="flex items-start justify-between">
                    <div className="flex items-center gap-3">
                        <div className="text-2xl bg-white/20 backdrop-blur-sm rounded-lg p-2">
                            {provider.logo}
                        </div>
                        <div>
                            <CardTitle className="text-lg text-white">{provider.name}</CardTitle>
                            <div className="flex items-center gap-2 mt-1">
                                {isConnected ? (
                                    <Badge variant="secondary" className="bg-white/20 text-white border-white/30">
                                        <CheckCircle className="h-3 w-3 mr-1" />
                                        Connected
                                    </Badge>
                                ) : (
                                    <Badge variant="outline" className="bg-white/10 text-white border-white/30">
                                        <AlertCircle className="h-3 w-3 mr-1" />
                                        Not Connected
                                    </Badge>
                                )}
                            </div>
                        </div>
                    </div>
                </div>

                <CardDescription className={isConnected ? "text-green-100" : "text-slate-200"}>
                    {provider.description}
                </CardDescription>
            </CardHeader>

            <CardContent className="p-6 space-y-6">
                {/* Models */}
                <div>
                    <h4 className="text-sm font-semibold mb-3 flex items-center gap-2 text-slate-700 dark:text-slate-300">
                        <Zap className="h-4 w-4" />
                        Available Models
                    </h4>
                    <div className="flex flex-wrap gap-2">
                        {provider.models.slice(0, 3).map((model) => (
                            <Badge key={model} variant="outline" className="text-xs border-slate-300 dark:border-slate-600">
                                {model}
                            </Badge>
                        ))}
                        {provider.models.length > 3 && (
                            <Badge variant="outline" className="text-xs border-slate-300 dark:border-slate-600">
                                +{provider.models.length - 3} more
                            </Badge>
                        )}
                    </div>
                </div>

                {/* Features */}
                <div>
                    <h4 className="text-sm font-semibold mb-3 flex items-center gap-2 text-slate-700 dark:text-slate-300">
                        <Shield className="h-4 w-4" />
                        Key Features
                    </h4>
                    <ul className="text-xs text-muted-foreground space-y-2">
                        {provider.features.slice(0, 3).map((feature) => (
                            <li key={feature} className="flex items-center gap-2">
                                <div className="w-1.5 h-1.5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full" />
                                {feature}
                            </li>
                        ))}
                    </ul>
                </div>

                {/* Pricing */}
                <div className="pt-4 border-t border-slate-200 dark:border-slate-700">
                    <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-slate-600 dark:text-slate-400">Pricing</span>
                        <span className="text-sm font-semibold text-slate-800 dark:text-slate-200">{provider.pricing}</span>
                    </div>
                </div>

                {/* Actions */}
                <div className="flex gap-3 pt-2">
                    {isConnected ? (
                        <>
                            <Button
                                variant="outline"
                                size="sm"
                                className="flex-1 border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800"
                                onClick={onConfigure}
                            >
                                <Settings className="h-4 w-4 mr-2" />
                                Configure
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                className="border-red-300 text-red-600 hover:bg-red-50 dark:border-red-600 dark:text-red-400 dark:hover:bg-red-900/20"
                            >
                                Disconnect
                            </Button>
                        </>
                    ) : (
                        <>
                            <Button
                                size="sm"
                                className="flex-1 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
                                onClick={onConfigure}
                            >
                                <Plug className="h-4 w-4 mr-2" />
                                Connect
                            </Button>
                            <Button
                                variant="outline"
                                size="sm"
                                className="border-slate-300 dark:border-slate-600 hover:bg-slate-50 dark:hover:bg-slate-800"
                            >
                                Learn More
                            </Button>
                        </>
                    )}
                </div>
            </CardContent>
        </Card>
    )
} 