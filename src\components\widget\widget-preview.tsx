"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { WidgetConfig } from "@/hooks/use-widget-config"
import { SharedWidgetPreview } from "@/components/widget/shared-widget-preview"
import { Monitor, Smartphone } from "lucide-react"

interface WidgetPreviewProps {
    config: WidgetConfig
}

export function WidgetPreview({ config }: WidgetPreviewProps) {
    return (
        <div className="w-full max-w-4xl mx-auto">
            <Tabs defaultValue="desktop" className="w-full space-y-6">
                {/* Enhanced Tab Navigation */}
                <div className="bg-white dark:bg-slate-900 rounded-2xl shadow-lg border border-slate-200 dark:border-slate-700 p-1">
                    <TabsList className="grid w-full grid-cols-3 bg-transparent gap-1 h-auto p-1">
                        <TabsTrigger
                            value="desktop"
                            className="flex items-center justify-center gap-1 rounded-lg py-2 px-2 text-xs font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white data-[state=active]:hover:from-blue-600 data-[state=active]:hover:to-purple-600"
                        >
                            <Monitor className="h-4 w-4" />
                            <span>Desktop</span>
                        </TabsTrigger>
                        <TabsTrigger
                            value="tablet"
                            className="flex items-center justify-center gap-1 rounded-lg py-2 px-2 text-xs font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-500 data-[state=active]:text-white data-[state=active]:hover:from-green-600 data-[state=active]:hover:to-emerald-600"
                        >
                            <Smartphone className="h-4 w-4 rotate-90" />
                            <span>Tablet</span>
                        </TabsTrigger>
                        <TabsTrigger
                            value="mobile"
                            className="flex items-center justify-center gap-1 rounded-lg py-2 px-2 text-xs font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-500 data-[state=active]:text-white data-[state=active]:hover:from-green-600 data-[state=active]:hover:to-emerald-600"
                        >
                            <Smartphone className="h-4 w-4" />
                            <span>Mobile</span>
                        </TabsTrigger>
                    </TabsList>
                </div>

                <TabsContent value="desktop">
                    <div className="relative bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-slate-800 dark:to-slate-900 rounded-lg p-8 min-h-[800px] overflow-visible">
                        {/* Desktop Browser Frame */}
                        <div className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl border border-slate-200 dark:border-slate-700 h-[700px] flex flex-col relative overflow-visible">
                            <div className="flex items-center gap-3 p-4 border-b border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800 rounded-t-xl flex-shrink-0">
                                <div className="flex gap-2">
                                    <div className="w-3 h-3 rounded-full bg-red-500 shadow-sm"></div>
                                    <div className="w-3 h-3 rounded-full bg-yellow-500 shadow-sm"></div>
                                    <div className="w-3 h-3 rounded-full bg-green-500 shadow-sm"></div>
                                </div>
                                <div className="flex-1 text-center">
                                    <div className="bg-white dark:bg-slate-700 rounded-lg px-4 py-1.5 text-sm text-slate-600 dark:text-slate-300 border border-slate-200 dark:border-slate-600 max-w-md mx-auto">
                                        🔒 demo-website.com/widget-preview
                                    </div>
                                </div>
                            </div>

                            <div className="flex-1 relative overflow-auto bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 rounded-b-xl">
                                <div className="absolute inset-0 flex items-center justify-center">
                                    <div className="text-center">
                                        <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-4 mx-auto shadow-lg">
                                            <Monitor className="h-8 w-8 text-white" />
                                        </div>
                                        <h3 className="text-xl font-semibold text-slate-800 dark:text-slate-200 mb-2">Your Website</h3>
                                        <p className="text-slate-500 dark:text-slate-400">
                                            The AI chat widget will appear here
                                        </p>
                                    </div>
                                </div>
                                <div className="absolute bottom-8 right-8 z-10">
                                    <SharedWidgetPreview config={config} isMobile={false} />
                                </div>
                            </div>
                        </div>
                    </div>
                </TabsContent>



                <TabsContent value="tablet">
                    <div className="relative bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-slate-800 dark:to-slate-900 rounded-lg p-8 min-h-[700px] flex items-center justify-center">
                        {/* Tablet Device Frame */}
                        <div className="relative">
                            <div className="w-[500px] h-[700px] bg-black rounded-[2rem] p-2 shadow-2xl">
                                <div className="w-full h-full bg-white dark:bg-slate-900 rounded-[1.8rem] overflow-auto relative">
                                    {/* Tablet Status Bar */}
                                    <div className="bg-slate-50 dark:bg-slate-800 px-6 py-2 flex justify-between items-center text-xs font-medium sticky top-0 z-10">
                                        <span>9:41</span>
                                        <div className="flex items-center gap-2">
                                            <div className="w-4 h-4 rounded-full bg-slate-300 dark:bg-slate-600"></div>
                                            <div className="w-4 h-4 rounded-full bg-slate-300 dark:bg-slate-600"></div>
                                        </div>
                                    </div>

                                    <div className="relative h-[650px] overflow-auto">
                                        <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
                                            <div className="text-center">
                                                <div className="w-14 h-14 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-3 mx-auto shadow-lg">
                                                    <Smartphone className="h-7 w-7 text-white rotate-90" />
                                                </div>
                                                <h3 className="text-xl font-semibold text-slate-800 dark:text-slate-200 mb-2">Tablet Website</h3>
                                                <p className="text-sm text-slate-500 dark:text-slate-400">
                                                    Widget preview
                                                </p>
                                            </div>
                                        </div>
                                        <div className="absolute bottom-8 right-8 pb-4">
                                            <SharedWidgetPreview config={config} isMobile={false} />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </TabsContent>

                <TabsContent value="mobile">
                    <div className="relative bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-slate-800 dark:to-slate-900 rounded-lg p-8 min-h-[700px] flex items-center justify-center">
                        {/* Mobile Device Frame */}
                        <div className="relative">
                            <div className="w-[360px] h-[720px] bg-black rounded-[2.5rem] p-2 shadow-2xl">
                                <div className="w-full h-full bg-white dark:bg-slate-900 rounded-[2rem] overflow-auto relative">
                                    {/* Mobile Status Bar */}
                                    <div className="bg-slate-50 dark:bg-slate-800 px-6 py-2 flex justify-between items-center text-xs font-medium sticky top-0 z-10">
                                        <span>9:41</span>
                                        <div className="flex items-center gap-1">
                                            <div className="w-4 h-2 border border-slate-400 rounded-sm">
                                                <div className="w-3 h-1 bg-green-500 rounded-sm m-0.5"></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div className="relative h-[660px] overflow-auto">
                                        <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
                                            <div className="text-center">
                                                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-3 mx-auto shadow-lg">
                                                    <Smartphone className="h-6 w-6 text-white" />
                                                </div>
                                                <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-2">Mobile Website</h3>
                                                <p className="text-sm text-slate-500 dark:text-slate-400">
                                                    Widget preview
                                                </p>
                                            </div>
                                        </div>
                                        <div className="absolute bottom-6 right-3 pb-4">
                                            <SharedWidgetPreview config={config} isMobile={true} />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </TabsContent>
            </Tabs>
        </div>
    )
} 