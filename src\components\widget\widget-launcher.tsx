"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { WidgetConfig } from "@/hooks/use-widget-config"
import { MessageSquare, X, ChevronUp, Sparkles } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

interface WidgetLauncherProps {
  config?: WidgetConfig
  className?: string
  onLaunch?: () => void
}

export function WidgetLauncher({ config, className = "", onLaunch }: WidgetLauncherProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [isVisible, setIsVisible] = useState(false)
  const [hasNotification, setHasNotification] = useState(false)

  // Default configuration
  const defaultConfig: Partial<WidgetConfig> = {
    primaryColor: "#3b82f6",
    chatButtonText: "Chat with us",
    position: "bottom-right",
  }

  const widgetConfig = { ...defaultConfig, ...config }

  // Simulate notification after delay
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true)
      
      // Add notification dot after another delay
      setTimeout(() => {
        setHasNotification(true)
      }, 3000)
    }, 1000)
    
    return () => clearTimeout(timer)
  }, [])

  // Handle launch
  const handleLaunch = () => {
    setHasNotification(false)
    if (onLaunch) onLaunch()
  }

  // Get position classes based on config
  const getPositionClasses = () => {
    switch (widgetConfig.position) {
      case "bottom-right":
        return "bottom-6 right-6"
      case "bottom-left":
        return "bottom-6 left-6"
      case "top-right":
        return "top-6 right-6"
      case "top-left":
        return "top-6 left-6"
      default:
        return "bottom-6 right-6"
    }
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div 
          className={`fixed ${getPositionClasses()} z-50 flex flex-col items-end gap-3 ${className}`}
          initial={{ opacity: 0, scale: 0.8, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.8, y: 20 }}
          transition={{ type: "spring", stiffness: 500, damping: 30 }}
        >
          {/* Notification bubble */}
          <AnimatePresence>
            {hasNotification && (
              <motion.div
                className="bg-white dark:bg-slate-800 rounded-xl p-3 shadow-lg max-w-xs"
                initial={{ opacity: 0, scale: 0.8, y: 10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8, y: 10 }}
                transition={{ type: "spring", stiffness: 500, damping: 30 }}
              >
                <div className="flex items-start gap-2">
                  <div className="flex-shrink-0">
                    <div 
                      className="w-8 h-8 rounded-full flex items-center justify-center"
                      style={{ background: `${widgetConfig.primaryColor}` }}
                    >
                      <Sparkles className="h-4 w-4 text-white" />
                    </div>
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium">Need help with anything?</p>
                    <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                      Our AI assistant is ready to answer your questions.
                    </p>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-6 w-6" 
                    onClick={() => setHasNotification(false)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
          
          {/* Launch button */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onMouseEnter={() => setIsHovered(true)}
            onMouseLeave={() => setIsHovered(false)}
          >
            <Button
              className="rounded-full shadow-lg flex items-center gap-2 pr-4 pl-3 py-6"
              style={{ 
                background: `${widgetConfig.primaryColor}`,
                boxShadow: isHovered ? `0 0 0 4px ${widgetConfig.primaryColor}20, 0 8px 16px -2px ${widgetConfig.primaryColor}30` : ''
              }}
              onClick={handleLaunch}
            >
              <AnimatePresence mode="wait">
                {isHovered ? (
                  <motion.div
                    key="chevron"
                    initial={{ rotate: -90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: 90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <ChevronUp className="h-5 w-5" />
                  </motion.div>
                ) : (
                  <motion.div
                    key="message"
                    initial={{ rotate: -90, opacity: 0 }}
                    animate={{ rotate: 0, opacity: 1 }}
                    exit={{ rotate: 90, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    <MessageSquare className="h-5 w-5" />
                  </motion.div>
                )}
              </AnimatePresence>
              <span>{widgetConfig.chatButtonText}</span>
              
              {/* Notification dot */}
              {hasNotification && (
                <motion.div 
                  className="absolute -top-1 -right-1 w-3 h-3 rounded-full bg-red-500"
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                />
              )}
            </Button>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
