"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"
import {
    LayoutDashboard,
    Puzzle,
    Settings,
    Code,
    TestTube,
    Zap,
} from "lucide-react"

const navigation = [
    {
        name: "Dashboard",
        href: "/",
        icon: LayoutDashboard,
    },
    {
        name: "Widget Builder",
        href: "/widget-builder",
        icon: Puzzle,
    },
    {
        name: "AI Providers",
        href: "/providers",
        icon: Zap,
    },
    {
        name: "Embed Code",
        href: "/embed-code",
        icon: Code,
    },
    {
        name: "Testing Lab",
        href: "/testing-lab",
        icon: TestTube,
    },
    {
        name: "Settings",
        href: "/settings",
        icon: Settings,
    },
]

export function SidebarNavigation() {
    const pathname = usePathname()

    return (
        <nav className="flex flex-col space-y-1">
            {navigation.map((item) => {
                const isActive = pathname === item.href
                return (
                    <Link
                        key={item.name}
                        href={item.href}
                        className={cn(
                            "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                            isActive
                                ? "bg-primary text-primary-foreground"
                                : "text-muted-foreground hover:bg-muted hover:text-foreground"
                        )}
                    >
                        <item.icon className="h-4 w-4" />
                        {item.name}
                    </Link>
                )
            })}
        </nav>
    )
} 