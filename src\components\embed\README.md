# Embed Code Generator Components

A professional, developer-friendly UI for generating and previewing chat widget embed codes.

## Components

### EmbedCodePanel
Main container component that orchestrates the entire embed code generation experience.

**Features:**
- Widget configuration display
- Integration method selection
- Two-column responsive layout (code + preview)
- Step-by-step implementation guide

### EmbedFormatSelector
Interactive selector for choosing integration methods.

**Supported Formats:**
- **HTML/JavaScript** - Standard script tag integration
- **React Component** - NPM package for React applications  
- **iFrame Embed** - Isolated iframe for maximum compatibility

### EmbedCodeBlock
Code display component with syntax highlighting and developer tools.

**Features:**
- Syntax-highlighted code blocks
- One-click copy to clipboard with toast feedback
- Download code as file (.html, .jsx)
- Language badges and metadata display
- Hover overlay copy button

### EmbedCodePreview
Interactive preview showing how the widget appears on websites.

**Features:**
- Desktop and mobile preview modes
- Mock browser/mobile frames
- Interactive widget simulation
- Widget open/close states
- Responsive design testing

## Usage

```tsx
import { EmbedCodePanel } from "@/components/embed/embed-code-panel"

export default function EmbedCodePage() {
  return (
    <div className="container mx-auto py-6">
      <EmbedCodePanel />
    </div>
  )
}
```

## Design Features

- **Clean & Minimal** - Focused on code clarity and usability
- **Developer-Focused** - Syntax highlighting, copy/download tools
- **Responsive Layout** - Two-column desktop, stacked mobile
- **Visual Feedback** - Toast notifications, state indicators
- **Professional Polish** - Consistent spacing, hover states, animations

## Integration Methods

### HTML/JavaScript
Standard script tag that works on any website. Most popular choice.

### React Component
NPM package for React applications. Developer-friendly with props.

### iFrame Embed
Isolated iframe for maximum compatibility and security.

## Technical Implementation

- **Toast System** - Sonner for user feedback
- **State Management** - React hooks for widget configuration
- **Responsive Design** - Tailwind CSS with mobile-first approach
- **Accessibility** - Proper ARIA labels and keyboard navigation
- **TypeScript** - Full type safety throughout components 