"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { toast } from "sonner"
import {
    User,
    Shield,
    Bell,
    Globe,
    Settings as SettingsIcon,
    Database,
    Eye,
    EyeOff,
    Upload,
    Download,
    Trash2,
    Key,
    Clock,
    Smartphone,
    Mail,
    MessageSquare,
    Palette,
    Languages,
    Calendar,
    MapPin,
    Save,
    RotateCcw
} from "lucide-react"

export default function SettingsPage() {
    // Profile Settings State
    const [profile, setProfile] = useState({
        firstName: "John",
        lastName: "Doe",
        email: "<EMAIL>",
        company: "Acme Corp",
        role: "Product Manager",
        bio: "Building amazing chat experiences for our customers."
    })

    // Security Settings State
    const [security, setSecurity] = useState({
        twoFactorEnabled: true,
        sessionTimeout: "30",
        apiKeysVisible: false,
        apiKeys: [
            { id: "1", name: "Production API", key: "sk-proj-abc123...", created: "2024-01-15", lastUsed: "2024-01-20" },
            { id: "2", name: "Development API", key: "sk-proj-def456...", created: "2024-01-10", lastUsed: "2024-01-19" }
        ]
    })

    // Notification Settings State
    const [notifications, setNotifications] = useState({
        emailNotifications: true,
        pushNotifications: true,
        widgetActivity: true,
        systemUpdates: false,
        marketingEmails: false,
        weeklyReports: true
    })

    // Appearance Settings State
    const [appearance, setAppearance] = useState({
        theme: "system",
        language: "en",
        timezone: "America/New_York",
        dateFormat: "MM/DD/YYYY",
        timeFormat: "12h"
    })

    // Widget Defaults State
    const [widgetDefaults, setWidgetDefaults] = useState({
        defaultTheme: "modern",
        defaultPosition: "bottom-right",
        defaultSize: "medium",
        autoSave: true,
        showPreview: true
    })

    // Data & Privacy State
    const [privacy, setPrivacy] = useState({
        dataRetention: "12",
        analyticsEnabled: true,
        cookieConsent: true,
        gdprCompliance: true,
        dataExportRequested: false
    })

    const handleSave = (section: string) => {
        toast.success(`${section} settings saved successfully!`)
    }

    const handleReset = (section: string) => {
        toast.info(`${section} settings reset to defaults`)
    }

    const toggleApiKeyVisibility = () => {
        setSecurity(prev => ({ ...prev, apiKeysVisible: !prev.apiKeysVisible }))
    }

    const generateNewApiKey = () => {
        const newKey = {
            id: Date.now().toString(),
            name: "New API Key",
            key: `sk-proj-${Math.random().toString(36).substring(2, 15)}...`,
            created: new Date().toISOString().split('T')[0],
            lastUsed: "Never"
        }
        setSecurity(prev => ({
            ...prev,
            apiKeys: [...prev.apiKeys, newKey]
        }))
        toast.success("New API key generated successfully!")
    }

    const deleteApiKey = (id: string) => {
        setSecurity(prev => ({
            ...prev,
            apiKeys: prev.apiKeys.filter(key => key.id !== id)
        }))
        toast.success("API key deleted successfully!")
    }

    const requestDataExport = () => {
        setPrivacy(prev => ({ ...prev, dataExportRequested: true }))
        toast.success("Data export request submitted. You'll receive an email when ready.")
    }

    const deleteAccount = () => {
        toast.error("Account deletion is a permanent action. Please contact support to proceed.")
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
            {/* Hero Header Section */}
            <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white">
                <div className="absolute inset-0 bg-black/20"></div>
                <div className="absolute inset-0 opacity-30" style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                }}></div>
                <div className="relative px-6 py-16">
                    <div className="w-full">
                        <div className="flex items-center gap-4 mb-4">
                            <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                                <SettingsIcon className="h-8 w-8" />
                            </div>
                            <div>
                                <h1 className="text-4xl font-bold mb-2">Settings & Preferences</h1>
                                <p className="text-blue-100 text-lg">
                                    Customize your experience and manage your account
                                </p>
                            </div>
                        </div>

                        {/* Quick Stats */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
                            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold">12</div>
                                <div className="text-sm text-blue-100">Active Widgets</div>
                            </div>
                            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold">3</div>
                                <div className="text-sm text-blue-100">API Keys</div>
                            </div>
                            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold">24/7</div>
                                <div className="text-sm text-blue-100">Uptime</div>
                            </div>
                            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold">99.9%</div>
                                <div className="text-sm text-blue-100">Reliability</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="w-full px-6 py-8">
                <Tabs defaultValue="profile" className="space-y-8">
                    {/* Enhanced Tab Navigation */}
                    <div className="bg-white dark:bg-slate-900 rounded-2xl shadow-lg border border-slate-200 dark:border-slate-700 p-1">
                        <TabsList className="grid w-full grid-cols-3 md:grid-cols-6 bg-transparent gap-1 h-auto p-1">
                            <TabsTrigger
                                value="profile"
                                className="flex items-center justify-center gap-2 rounded-lg py-2.5 px-3 text-sm font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white data-[state=active]:hover:from-blue-600 data-[state=active]:hover:to-purple-600"
                            >
                                <User className="h-4 w-4" />
                                <span className="hidden sm:inline">Profile</span>
                            </TabsTrigger>
                            <TabsTrigger
                                value="security"
                                className="flex items-center justify-center gap-2 rounded-lg py-2.5 px-3 text-sm font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-500 data-[state=active]:to-emerald-500 data-[state=active]:text-white data-[state=active]:hover:from-green-600 data-[state=active]:hover:to-emerald-600"
                            >
                                <Shield className="h-4 w-4" />
                                <span className="hidden sm:inline">Security</span>
                            </TabsTrigger>
                            <TabsTrigger
                                value="notifications"
                                className="flex items-center justify-center gap-2 rounded-lg py-2.5 px-3 text-sm font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-orange-500 data-[state=active]:to-red-500 data-[state=active]:text-white data-[state=active]:hover:from-orange-600 data-[state=active]:hover:to-red-600"
                            >
                                <Bell className="h-4 w-4" />
                                <span className="hidden sm:inline">Notifications</span>
                            </TabsTrigger>
                            <TabsTrigger
                                value="appearance"
                                className="flex items-center justify-center gap-2 rounded-lg py-2.5 px-3 text-sm font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-pink-500 data-[state=active]:to-rose-500 data-[state=active]:text-white data-[state=active]:hover:from-pink-600 data-[state=active]:hover:to-rose-600"
                            >
                                <Globe className="h-4 w-4" />
                                <span className="hidden sm:inline">Appearance</span>
                            </TabsTrigger>
                            <TabsTrigger
                                value="widgets"
                                className="flex items-center justify-center gap-2 rounded-lg py-2.5 px-3 text-sm font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-indigo-500 data-[state=active]:to-blue-500 data-[state=active]:text-white data-[state=active]:hover:from-indigo-600 data-[state=active]:hover:to-blue-600"
                            >
                                <SettingsIcon className="h-4 w-4" />
                                <span className="hidden sm:inline">Widgets</span>
                            </TabsTrigger>
                            <TabsTrigger
                                value="privacy"
                                className="flex items-center justify-center gap-2 rounded-lg py-2.5 px-3 text-sm font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-violet-500 data-[state=active]:text-white data-[state=active]:hover:from-purple-600 data-[state=active]:hover:to-violet-600"
                            >
                                <Database className="h-4 w-4" />
                                <span className="hidden sm:inline">Privacy</span>
                            </TabsTrigger>
                        </TabsList>
                    </div>

                    {/* Profile Settings */}
                    <TabsContent value="profile">
                        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                            <div className="lg:col-span-2">
                                <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-blue-50 dark:from-slate-900 dark:to-slate-800 overflow-hidden">
                                    <CardHeader className="bg-gradient-to-r from-blue-500 to-purple-500 text-white pb-4 pt-6  rounded-t-lg">
                                        <CardTitle className="flex items-center gap-2">
                                            <User className="h-5 w-5" />
                                            Profile Information
                                        </CardTitle>
                                        <CardDescription className="text-blue-100">
                                            Update your personal information and profile details
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="p-6 space-y-6">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="firstName">First Name</Label>
                                                <Input
                                                    id="firstName"
                                                    value={profile.firstName}
                                                    onChange={(e) => setProfile(prev => ({ ...prev, firstName: e.target.value }))}
                                                    className="border-2 focus:border-blue-500"
                                                />
                                            </div>
                                            <div className="space-y-2">
                                                <Label htmlFor="lastName">Last Name</Label>
                                                <Input
                                                    id="lastName"
                                                    value={profile.lastName}
                                                    onChange={(e) => setProfile(prev => ({ ...prev, lastName: e.target.value }))}
                                                    className="border-2 focus:border-blue-500"
                                                />
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="email">Email Address</Label>
                                            <Input
                                                id="email"
                                                type="email"
                                                value={profile.email}
                                                onChange={(e) => setProfile(prev => ({ ...prev, email: e.target.value }))}
                                                className="border-2 focus:border-blue-500"
                                            />
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <Label htmlFor="company">Company</Label>
                                                <Input
                                                    id="company"
                                                    value={profile.company}
                                                    onChange={(e) => setProfile(prev => ({ ...prev, company: e.target.value }))}
                                                    className="border-2 focus:border-blue-500"
                                                />
                                            </div>
                                            <div className="space-y-2">
                                                <Label htmlFor="role">Role</Label>
                                                <Input
                                                    id="role"
                                                    value={profile.role}
                                                    onChange={(e) => setProfile(prev => ({ ...prev, role: e.target.value }))}
                                                    className="border-2 focus:border-blue-500"
                                                />
                                            </div>
                                        </div>

                                        <div className="space-y-2">
                                            <Label htmlFor="bio">Bio</Label>
                                            <Textarea
                                                id="bio"
                                                value={profile.bio}
                                                onChange={(e) => setProfile(prev => ({ ...prev, bio: e.target.value }))}
                                                rows={3}
                                                className="border-2 focus:border-blue-500"
                                            />
                                        </div>

                                        <div className="flex gap-2 pt-4">
                                            <Button onClick={() => handleSave("Profile")} className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600">
                                                <Save className="h-4 w-4 mr-2" />
                                                Save Changes
                                            </Button>
                                            <Button variant="outline" onClick={() => handleReset("Profile")}>
                                                <RotateCcw className="h-4 w-4 mr-2" />
                                                Reset
                                            </Button>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>

                            <div className="space-y-6">
                                <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-purple-50 dark:from-slate-900 dark:to-slate-800 overflow-hidden">
                                    <CardHeader className="bg-gradient-to-r from-purple-500 to-pink-500 text-white pb-4 pt-6  rounded-t-lg">
                                        <CardTitle>Profile Picture</CardTitle>
                                    </CardHeader>
                                    <CardContent className="p-6 text-center">
                                        <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-2xl mx-auto mb-4 shadow-lg">
                                            {profile.firstName[0]}{profile.lastName[0]}
                                        </div>
                                        <Button variant="outline" size="sm" className="w-full">
                                            <Upload className="h-4 w-4 mr-2" />
                                            Upload New Picture
                                        </Button>
                                    </CardContent>
                                </Card>

                                <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-green-50 dark:from-slate-900 dark:to-slate-800 overflow-hidden">
                                    <CardHeader className="bg-gradient-to-r from-green-500 to-emerald-500 text-white pb-4 pt-6  rounded-t-lg">
                                        <CardTitle>Account Status</CardTitle>
                                    </CardHeader>
                                    <CardContent className="p-6 space-y-3">
                                        <div className="flex justify-between items-center">
                                            <span className="text-sm">Account Type</span>
                                            <Badge className="bg-gradient-to-r from-green-500 to-emerald-500">Premium</Badge>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-sm">Member Since</span>
                                            <span className="text-sm font-medium">Jan 2024</span>
                                        </div>
                                        <div className="flex justify-between items-center">
                                            <span className="text-sm">Last Login</span>
                                            <span className="text-sm font-medium">Today</span>
                                        </div>
                                    </CardContent>
                                </Card>
                            </div>
                        </div>
                    </TabsContent>

                    {/* Security Settings */}
                    <TabsContent value="security">
                        <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                            <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-green-50 dark:from-slate-900 dark:to-slate-800 overflow-hidden">
                                <CardHeader className="bg-gradient-to-r from-green-500 to-emerald-500 text-white pb-4 pt-6  rounded-t-lg">
                                    <CardTitle className="flex items-center gap-2">
                                        <Shield className="h-5 w-5" />
                                        Security Settings
                                    </CardTitle>
                                    <CardDescription className="text-green-100">
                                        Manage your account security and authentication
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="p-6 space-y-6">
                                    <div className="flex items-center justify-between p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                        <div className="space-y-1">
                                            <Label>Two-Factor Authentication</Label>
                                            <p className="text-sm text-muted-foreground">
                                                Add an extra layer of security to your account
                                            </p>
                                        </div>
                                        <Switch
                                            checked={security.twoFactorEnabled}
                                            onCheckedChange={(checked) =>
                                                setSecurity(prev => ({ ...prev, twoFactorEnabled: checked }))
                                            }
                                        />
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
                                        <Select
                                            value={security.sessionTimeout}
                                            onValueChange={(value) =>
                                                setSecurity(prev => ({ ...prev, sessionTimeout: value }))
                                            }
                                        >
                                            <SelectTrigger className="border-2 focus:border-green-500">
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="15">15 minutes</SelectItem>
                                                <SelectItem value="30">30 minutes</SelectItem>
                                                <SelectItem value="60">1 hour</SelectItem>
                                                <SelectItem value="120">2 hours</SelectItem>
                                                <SelectItem value="480">8 hours</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="flex gap-2 pt-4">
                                        <Button onClick={() => handleSave("Security")} className="bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600">
                                            <Save className="h-4 w-4 mr-2" />
                                            Save Changes
                                        </Button>
                                        <Button variant="outline" onClick={() => handleReset("Security")}>
                                            <RotateCcw className="h-4 w-4 mr-2" />
                                            Reset
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-yellow-50 dark:from-slate-900 dark:to-slate-800 overflow-hidden">
                                <CardHeader className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white pb-4 pt-6  rounded-t-lg">
                                    <CardTitle className="flex items-center gap-2">
                                        <Key className="h-5 w-5" />
                                        API Keys Management
                                    </CardTitle>
                                    <CardDescription className="text-yellow-100">
                                        Manage your API keys for integrations
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="p-6 space-y-4">
                                    <div className="flex justify-between items-center">
                                        <Button onClick={generateNewApiKey} size="sm" className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600">
                                            Generate New Key
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={toggleApiKeyVisibility}
                                        >
                                            {security.apiKeysVisible ? (
                                                <>
                                                    <EyeOff className="h-4 w-4 mr-2" />
                                                    Hide Keys
                                                </>
                                            ) : (
                                                <>
                                                    <Eye className="h-4 w-4 mr-2" />
                                                    Show Keys
                                                </>
                                            )}
                                        </Button>
                                    </div>

                                    <div className="space-y-3">
                                        {security.apiKeys.map((apiKey) => (
                                            <div key={apiKey.id} className="border-2 border-yellow-200 dark:border-yellow-800 rounded-lg p-4 bg-yellow-50 dark:bg-yellow-900/20">
                                                <div className="flex justify-between items-start mb-2">
                                                    <div>
                                                        <h4 className="font-medium">{apiKey.name}</h4>
                                                        <p className="text-sm text-muted-foreground font-mono">
                                                            {security.apiKeysVisible ? apiKey.key : "sk-proj-••••••••••••••••"}
                                                        </p>
                                                    </div>
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => deleteApiKey(apiKey.id)}
                                                        className="text-red-600 hover:text-red-700"
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                                <div className="flex gap-4 text-xs text-muted-foreground">
                                                    <span className="flex items-center gap-1">
                                                        <Calendar className="h-3 w-3" />
                                                        Created: {apiKey.created}
                                                    </span>
                                                    <span className="flex items-center gap-1">
                                                        <Clock className="h-3 w-3" />
                                                        Last used: {apiKey.lastUsed}
                                                    </span>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    {/* Notification Settings */}
                    <TabsContent value="notifications">
                        <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-orange-50 dark:from-slate-900 dark:to-slate-800 overflow-hidden">
                            <CardHeader className="bg-gradient-to-r from-orange-500 to-red-500 text-white pb-4 pt-6  rounded-t-lg">
                                <CardTitle className="flex items-center gap-2">
                                    <Bell className="h-5 w-5" />
                                    Notification Preferences
                                </CardTitle>
                                <CardDescription className="text-orange-100">
                                    Choose how and when you want to be notified
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="p-6 -mt-2 space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="space-y-4">
                                        <h3 className="font-semibold text-lg">Communication</h3>
                                        <div className="space-y-4">
                                            <div className="flex items-center justify-between p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                                                <div className="space-y-1">
                                                    <Label className="flex items-center gap-2">
                                                        <Mail className="h-4 w-4" />
                                                        Email Notifications
                                                    </Label>
                                                    <p className="text-sm text-muted-foreground">
                                                        Receive notifications via email
                                                    </p>
                                                </div>
                                                <Switch
                                                    checked={notifications.emailNotifications}
                                                    onCheckedChange={(checked) =>
                                                        setNotifications(prev => ({ ...prev, emailNotifications: checked }))
                                                    }
                                                />
                                            </div>

                                            <div className="flex items-center justify-between p-4 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                                                <div className="space-y-1">
                                                    <Label className="flex items-center gap-2">
                                                        <Smartphone className="h-4 w-4" />
                                                        Push Notifications
                                                    </Label>
                                                    <p className="text-sm text-muted-foreground">
                                                        Receive push notifications in your browser
                                                    </p>
                                                </div>
                                                <Switch
                                                    checked={notifications.pushNotifications}
                                                    onCheckedChange={(checked) =>
                                                        setNotifications(prev => ({ ...prev, pushNotifications: checked }))
                                                    }
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    <div className="space-y-4">
                                        <h3 className="font-semibold text-lg">Content</h3>
                                        <div className="space-y-4">
                                            <div className="flex items-center justify-between p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                                                <div className="space-y-1">
                                                    <Label className="flex items-center gap-2">
                                                        <MessageSquare className="h-4 w-4" />
                                                        Widget Activity
                                                    </Label>
                                                    <p className="text-sm text-muted-foreground">
                                                        Notifications about widget interactions
                                                    </p>
                                                </div>
                                                <Switch
                                                    checked={notifications.widgetActivity}
                                                    onCheckedChange={(checked) =>
                                                        setNotifications(prev => ({ ...prev, widgetActivity: checked }))
                                                    }
                                                />
                                            </div>

                                            <div className="flex items-center justify-between p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                                                <div className="space-y-1">
                                                    <Label>System Updates</Label>
                                                    <p className="text-sm text-muted-foreground">
                                                        Important updates about the platform
                                                    </p>
                                                </div>
                                                <Switch
                                                    checked={notifications.systemUpdates}
                                                    onCheckedChange={(checked) =>
                                                        setNotifications(prev => ({ ...prev, systemUpdates: checked }))
                                                    }
                                                />
                                            </div>

                                            <div className="flex items-center justify-between p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                                                <div className="space-y-1">
                                                    <Label>Weekly Reports</Label>
                                                    <p className="text-sm text-muted-foreground">
                                                        Weekly summary of widget performance
                                                    </p>
                                                </div>
                                                <Switch
                                                    checked={notifications.weeklyReports}
                                                    onCheckedChange={(checked) =>
                                                        setNotifications(prev => ({ ...prev, weeklyReports: checked }))
                                                    }
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div className="flex gap-2 pt-4">
                                    <Button onClick={() => handleSave("Notification")} className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600">
                                        <Save className="h-4 w-4 mr-2" />
                                        Save Changes
                                    </Button>
                                    <Button variant="outline" onClick={() => handleReset("Notification")}>
                                        <RotateCcw className="h-4 w-4 mr-2" />
                                        Reset
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Appearance & Localization */}
                    <TabsContent value="appearance">
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-pink-50 dark:from-slate-900 dark:to-slate-800 overflow-hidden">
                                <CardHeader className="bg-gradient-to-r from-pink-500 to-rose-500 text-white pb-4 pt-6  rounded-t-lg">
                                    <CardTitle className="flex items-center gap-2">
                                        <Palette className="h-5 w-5" />
                                        Appearance Settings
                                    </CardTitle>
                                    <CardDescription className="text-pink-100">
                                        Customize your interface appearance
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="p-6 -mt-2 space-y-6">
                                    <div className="space-y-2">
                                        <Label className="flex items-center gap-2">
                                            <Palette className="h-4 w-4" />
                                            Theme
                                        </Label>
                                        <Select
                                            value={appearance.theme}
                                            onValueChange={(value) =>
                                                setAppearance(prev => ({ ...prev, theme: value }))
                                            }
                                        >
                                            <SelectTrigger className="border-2 focus:border-pink-500">
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="light">Light</SelectItem>
                                                <SelectItem value="dark">Dark</SelectItem>
                                                <SelectItem value="system">System</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="space-y-2">
                                        <Label className="flex items-center gap-2">
                                            <Languages className="h-4 w-4" />
                                            Language
                                        </Label>
                                        <Select
                                            value={appearance.language}
                                            onValueChange={(value) =>
                                                setAppearance(prev => ({ ...prev, language: value }))
                                            }
                                        >
                                            <SelectTrigger className="border-2 focus:border-pink-500">
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="en">English</SelectItem>
                                                <SelectItem value="es">Spanish</SelectItem>
                                                <SelectItem value="fr">French</SelectItem>
                                                <SelectItem value="de">German</SelectItem>
                                                <SelectItem value="ja">Japanese</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="flex gap-2 pt-4">
                                        <Button onClick={() => handleSave("Appearance")} className="bg-gradient-to-r from-pink-500 to-rose-500 hover:from-pink-600 hover:to-rose-600">
                                            <Save className="h-4 w-4 mr-2" />
                                            Save Changes
                                        </Button>
                                        <Button variant="outline" onClick={() => handleReset("Appearance")}>
                                            <RotateCcw className="h-4 w-4 mr-2" />
                                            Reset
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-indigo-50 dark:from-slate-900 dark:to-slate-800 overflow-hidden">
                                <CardHeader className="bg-gradient-to-r from-indigo-500 to-blue-500 text-white pb-4 pt-6  rounded-t-lg">
                                    <CardTitle className="flex items-center gap-2">
                                        <Globe className="h-5 w-5" />
                                        Localization Settings
                                    </CardTitle>
                                    <CardDescription className="text-indigo-100">
                                        Configure regional and time settings
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="p-6 -mt-2 space-y-6">
                                    <div className="space-y-2">
                                        <Label className="flex items-center gap-2">
                                            <MapPin className="h-4 w-4" />
                                            Timezone
                                        </Label>
                                        <Select
                                            value={appearance.timezone}
                                            onValueChange={(value) =>
                                                setAppearance(prev => ({ ...prev, timezone: value }))
                                            }
                                        >
                                            <SelectTrigger className="border-2 focus:border-indigo-500">
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="America/New_York">Eastern Time (ET)</SelectItem>
                                                <SelectItem value="America/Chicago">Central Time (CT)</SelectItem>
                                                <SelectItem value="America/Denver">Mountain Time (MT)</SelectItem>
                                                <SelectItem value="America/Los_Angeles">Pacific Time (PT)</SelectItem>
                                                <SelectItem value="Europe/London">London (GMT)</SelectItem>
                                                <SelectItem value="Europe/Paris">Paris (CET)</SelectItem>
                                                <SelectItem value="Asia/Tokyo">Tokyo (JST)</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div className="space-y-2">
                                            <Label className="flex items-center gap-2">
                                                <Calendar className="h-4 w-4" />
                                                Date Format
                                            </Label>
                                            <Select
                                                value={appearance.dateFormat}
                                                onValueChange={(value) =>
                                                    setAppearance(prev => ({ ...prev, dateFormat: value }))
                                                }
                                            >
                                                <SelectTrigger className="border-2 focus:border-indigo-500">
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                                                    <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                                                    <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="space-y-2">
                                            <Label className="flex items-center gap-2">
                                                <Clock className="h-4 w-4" />
                                                Time Format
                                            </Label>
                                            <Select
                                                value={appearance.timeFormat}
                                                onValueChange={(value) =>
                                                    setAppearance(prev => ({ ...prev, timeFormat: value }))
                                                }
                                            >
                                                <SelectTrigger className="border-2 focus:border-indigo-500">
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="12h">12 Hour</SelectItem>
                                                    <SelectItem value="24h">24 Hour</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    {/* Widget Defaults */}
                    <TabsContent value="widgets">
                        <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-indigo-50 dark:from-slate-900 dark:to-slate-800 overflow-hidden">
                            <CardHeader className="bg-gradient-to-r from-indigo-500 to-blue-500 text-white pb-4 pt-6  rounded-t-lg">
                                <CardTitle className="flex items-center gap-2">
                                    <SettingsIcon className="h-5 w-5" />
                                    Widget Default Settings
                                </CardTitle>
                                <CardDescription className="text-indigo-100">
                                    Set default configurations for new widgets
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="p-6 -mt-2 space-y-6">
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div className="space-y-2">
                                        <Label>Default Theme</Label>
                                        <Select
                                            value={widgetDefaults.defaultTheme}
                                            onValueChange={(value) =>
                                                setWidgetDefaults(prev => ({ ...prev, defaultTheme: value }))
                                            }
                                        >
                                            <SelectTrigger className="border-2 focus:border-indigo-500">
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="modern">Modern</SelectItem>
                                                <SelectItem value="classic">Classic</SelectItem>
                                                <SelectItem value="minimal">Minimal</SelectItem>
                                                <SelectItem value="colorful">Colorful</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="space-y-2">
                                        <Label>Default Position</Label>
                                        <Select
                                            value={widgetDefaults.defaultPosition}
                                            onValueChange={(value) =>
                                                setWidgetDefaults(prev => ({ ...prev, defaultPosition: value }))
                                            }
                                        >
                                            <SelectTrigger className="border-2 focus:border-indigo-500">
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="bottom-right">Bottom Right</SelectItem>
                                                <SelectItem value="bottom-left">Bottom Left</SelectItem>
                                                <SelectItem value="top-right">Top Right</SelectItem>
                                                <SelectItem value="top-left">Top Left</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="space-y-2">
                                        <Label>Default Size</Label>
                                        <Select
                                            value={widgetDefaults.defaultSize}
                                            onValueChange={(value) =>
                                                setWidgetDefaults(prev => ({ ...prev, defaultSize: value }))
                                            }
                                        >
                                            <SelectTrigger className="border-2 focus:border-indigo-500">
                                                <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="small">Small</SelectItem>
                                                <SelectItem value="medium">Medium</SelectItem>
                                                <SelectItem value="large">Large</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="flex items-center justify-between p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
                                        <div className="space-y-1">
                                            <Label>Auto-save Changes</Label>
                                            <p className="text-sm text-muted-foreground">
                                                Automatically save widget configurations
                                            </p>
                                        </div>
                                        <Switch
                                            checked={widgetDefaults.autoSave}
                                            onCheckedChange={(checked) =>
                                                setWidgetDefaults(prev => ({ ...prev, autoSave: checked }))
                                            }
                                        />
                                    </div>

                                    <div className="flex items-center justify-between p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
                                        <div className="space-y-1">
                                            <Label>Show Preview by Default</Label>
                                            <p className="text-sm text-muted-foreground">
                                                Always show widget preview when editing
                                            </p>
                                        </div>
                                        <Switch
                                            checked={widgetDefaults.showPreview}
                                            onCheckedChange={(checked) =>
                                                setWidgetDefaults(prev => ({ ...prev, showPreview: checked }))
                                            }
                                        />
                                    </div>
                                </div>

                                <div className="flex gap-2 pt-4">
                                    <Button onClick={() => handleSave("Widget Defaults")} className="bg-gradient-to-r from-indigo-500 to-blue-500 hover:from-indigo-600 hover:to-blue-600">
                                        <Save className="h-4 w-4 mr-2" />
                                        Save Changes
                                    </Button>
                                    <Button variant="outline" onClick={() => handleReset("Widget Defaults")}>
                                        <RotateCcw className="h-4 w-4 mr-2" />
                                        Reset
                                    </Button>
                                </div>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    {/* Data & Privacy */}
                    <TabsContent value="privacy">
                        <div className="space-y-8">
                            <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-purple-50 dark:from-slate-900 dark:to-slate-800 overflow-hidden">
                                <CardHeader className="bg-gradient-to-r from-purple-500 to-violet-500 text-white pb-4 pt-6  rounded-t-lg">
                                    <CardTitle className="flex items-center gap-2">
                                        <Database className="h-5 w-5" />
                                        Data & Privacy Settings
                                    </CardTitle>
                                    <CardDescription className="text-purple-100">
                                        Manage your data retention and privacy preferences
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="p-6 -mt-2 space-y-6">
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div className="space-y-2">
                                            <Label>Data Retention Period</Label>
                                            <Select
                                                value={privacy.dataRetention}
                                                onValueChange={(value) =>
                                                    setPrivacy(prev => ({ ...prev, dataRetention: value }))
                                                }
                                            >
                                                <SelectTrigger className="border-2 focus:border-purple-500">
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="3">3 months</SelectItem>
                                                    <SelectItem value="6">6 months</SelectItem>
                                                    <SelectItem value="12">12 months</SelectItem>
                                                    <SelectItem value="24">24 months</SelectItem>
                                                    <SelectItem value="indefinite">Indefinite</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div className="space-y-4">
                                            <div className="flex items-center justify-between p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                                                <div className="space-y-1">
                                                    <Label>Analytics & Usage Data</Label>
                                                    <p className="text-sm text-muted-foreground">
                                                        Help us improve by sharing anonymous usage data
                                                    </p>
                                                </div>
                                                <Switch
                                                    checked={privacy.analyticsEnabled}
                                                    onCheckedChange={(checked) =>
                                                        setPrivacy(prev => ({ ...prev, analyticsEnabled: checked }))
                                                    }
                                                />
                                            </div>

                                            <div className="flex items-center justify-between p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                                                <div className="space-y-1">
                                                    <Label>GDPR Compliance</Label>
                                                    <p className="text-sm text-muted-foreground">
                                                        Enable GDPR compliance features
                                                    </p>
                                                </div>
                                                <Switch
                                                    checked={privacy.gdprCompliance}
                                                    onCheckedChange={(checked) =>
                                                        setPrivacy(prev => ({ ...prev, gdprCompliance: checked }))
                                                    }
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    <div className="flex gap-2 pt-4">
                                        <Button onClick={() => handleSave("Privacy")} className="bg-gradient-to-r from-purple-500 to-violet-500 hover:from-purple-600 hover:to-violet-600">
                                            <Save className="h-4 w-4 mr-2" />
                                            Save Changes
                                        </Button>
                                        <Button variant="outline" onClick={() => handleReset("Privacy")}>
                                            <RotateCcw className="h-4 w-4 mr-2" />
                                            Reset
                                        </Button>
                                    </div>
                                </CardContent>
                            </Card>

                            {/* Data Export & Account Management */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                                <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-blue-50 dark:from-slate-900 dark:to-slate-800 overflow-hidden">
                                    <CardHeader className="bg-gradient-to-r from-blue-500 to-cyan-500 text-white pb-4 pt-6  rounded-t-lg">
                                        <CardTitle>Data Export</CardTitle>
                                        <CardDescription className="text-blue-100">
                                            Download a copy of all your data
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="p-6 -mt-2 text-center">
                                        <div className="mb-4">
                                            <Download className="h-12 w-12 mx-auto text-blue-500 mb-2" />
                                            <p className="text-sm text-muted-foreground">
                                                Export your account data, widget configurations, and analytics
                                            </p>
                                        </div>
                                        <Button
                                            onClick={requestDataExport}
                                            disabled={privacy.dataExportRequested}
                                            className="w-full bg-gradient-to-r from-blue-500 to-cyan-500 hover:from-blue-600 hover:to-cyan-600"
                                        >
                                            <Download className="h-4 w-4 mr-2" />
                                            {privacy.dataExportRequested ? "Export Requested" : "Request Data Export"}
                                        </Button>
                                    </CardContent>
                                </Card>

                                <Card className="shadow-xl border-0 bg-gradient-to-br from-white to-red-50 dark:from-slate-900 dark:to-slate-800 overflow-hidden">
                                    <CardHeader className="bg-gradient-to-r from-red-500 to-pink-500 text-white pb-4 pt-6  rounded-t-lg">
                                        <CardTitle>Danger Zone</CardTitle>
                                        <CardDescription className="text-red-100">
                                            Irreversible account actions
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="p-6 -mt-2 text-center">
                                        <div className="mb-4">
                                            <Trash2 className="h-12 w-12 mx-auto text-red-500 mb-2" />
                                            <p className="text-sm text-muted-foreground">
                                                Permanently delete your account and all associated data
                                            </p>
                                        </div>
                                        <Button
                                            variant="destructive"
                                            onClick={deleteAccount}
                                            className="w-full bg-gradient-to-r from-red-500 to-pink-500 hover:from-red-600 hover:to-pink-600"
                                        >
                                            <Trash2 className="h-4 w-4 mr-2" />
                                            Delete Account
                                        </Button>
                                    </CardContent>
                                </Card>
                            </div>
                        </div>
                    </TabsContent>
                </Tabs>
            </div>
        </div >
    )
} 