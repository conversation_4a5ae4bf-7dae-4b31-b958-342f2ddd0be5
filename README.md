# ChatWidget Builder - UI Foundation

A production-grade, multi-AI chat widget builder SaaS platform built with Next.js 15, TypeScript, and Tailwind CSS.

## 🚀 Features

- **Modern Tech Stack**: Next.js 15 with App Router, TypeScript, Tailwind CSS v4
- **Responsive Design**: Mobile-first approach with collapsible sidebar
- **Dark/Light Mode**: Complete theme switching with next-themes
- **Component Library**: ShadCN UI components for consistent design
- **Clean Architecture**: Modular component structure for scalability

## 📁 Project Structure

```
src/
├── app/                          # Next.js App Router pages
│   ├── layout.tsx               # Global layout with sidebar + topbar
│   ├── page.tsx                 # Dashboard landing page
│   ├── widget-builder/          # Widget builder section
│   ├── providers/               # AI providers management
│   ├── embed-code/              # Embed code generation
│   ├── testing-lab/             # Widget testing environment
│   └── settings/                # Application settings
├── components/
│   ├── layout/                  # Layout-specific components
│   │   ├── sidebar-navigation.tsx
│   │   ├── topbar-header.tsx
│   │   ├── theme-toggle.tsx
│   │   ├── main-layout-wrapper.tsx
│   │   └── toaster.tsx
│   ├── ui/                      # ShadCN UI components
│   └── theme-provider.tsx       # Theme provider wrapper
├── hooks/
│   └── use-page-title.ts        # Dynamic page title management
└── lib/
    └── utils.ts                 # Utility functions
```

## 🎨 UI Components

### Layout Components

- **SidebarNavigation**: Fixed sidebar with navigation links and active state
- **TopbarHeader**: Sticky header with page title, theme toggle, and user avatar
- **ThemeToggle**: Light/dark mode switcher with smooth transitions
- **MainLayoutWrapper**: Consistent content padding and scrollable area
- **Toaster**: Notification system using ShadCN's Sonner

### Responsive Behavior

- **Desktop**: Fixed sidebar (256px width) with main content area
- **Mobile**: Collapsible sidebar using Sheet component
- **Tablet**: Responsive grid layouts and spacing

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4
- **Components**: ShadCN UI
- **Icons**: Lucide React
- **Theming**: next-themes

## 🚦 Getting Started

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Start Development Server**
   ```bash
   npm run dev
   ```

3. **Open Browser**
   Navigate to `http://localhost:3000`

## 📱 Navigation Structure

- **Dashboard** (`/`) - Overview and metrics
- **Widget Builder** (`/widget-builder`) - Create and customize widgets
- **AI Providers** (`/providers`) - Manage AI service providers
- **Embed Code** (`/embed-code`) - Generate embed codes
- **Testing Lab** (`/testing-lab`) - Test widgets before deployment
- **Settings** (`/settings`) - Application configuration

## 🎯 Design System

### Colors
- Uses Tailwind's neutral color palette
- Supports both light and dark themes
- Consistent color tokens across components

### Typography
- Geist Sans for UI text
- Geist Mono for code elements
- Responsive font sizes and line heights

### Spacing
- Consistent padding and margins using Tailwind's spacing scale
- Responsive spacing adjustments for different screen sizes

### Components
- Clean, minimal SaaS dashboard aesthetic
- Consistent hover states and transitions
- Accessible design with proper ARIA labels

## 🔧 Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 📋 Next Steps

This UI foundation provides the structural base for implementing:

1. **Widget Configuration Logic** - Forms and controls for widget customization
2. **AI Provider Integration** - API configuration and management interfaces
3. **Embed Code Generation** - Dynamic code generation and preview
4. **Testing Environment** - Live widget preview and testing tools
5. **Settings Management** - User preferences and application configuration

## 🏗️ Architecture Notes

- **Modular Design**: Each component is self-contained and reusable
- **Type Safety**: Full TypeScript coverage for better development experience
- **Performance**: Optimized with Next.js 15 and Turbopack
- **Accessibility**: WCAG compliant with proper semantic HTML
- **Scalability**: Clean separation of concerns for easy feature additions

The foundation is ready for business logic implementation while maintaining clean, maintainable code structure.
