import { WidgetConfig } from '@/hooks/use-widget-config'

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api'

// Types
export interface ApiResponse<T = any> {
    status: 'success' | 'error'
    message?: string
    data?: T
    errors?: Record<string, string[]>
}

export interface Widget extends WidgetConfig {
    id: number
    name: string
    slug: string
    user_id: number
    is_active: boolean
    views: number
    interactions: number
    created_at: string
    updated_at: string
}

export interface CreateWidgetRequest {
    name: string
    // All WidgetConfig fields are optional for creation (have defaults)
    primary_color?: string
    secondary_color?: string
    background_color?: string
    text_color?: string
    border_color?: string
    font_family?: string
    font_size?: string
    border_radius?: string
    border_width?: string
    shadow_size?: string
    position?: string
    widget_size?: string
    margin_x?: number
    margin_y?: number
    auto_open?: boolean
    show_on_mobile?: boolean
    allow_minimize?: boolean
    auto_open_delay?: number
    sound_notifications?: boolean
    typing_indicator?: boolean
    message_timestamps?: boolean
    response_delay?: number
    z_index?: number
    animation_style?: string
    welcome_title?: string
    welcome_message?: string
    welcome_delay?: number
    input_placeholder?: string
    max_message_length?: number
    chat_button_text?: string
    send_button_text?: string
    close_button_text?: string
    minimize_button_text?: string
    quick_replies?: string[]
    connection_error?: string
    typing_message?: string
    offline_message?: string
    company_name?: string
    company_logo?: string
    agent_name?: string
    agent_avatar?: string
    show_powered_by?: boolean
    powered_by_text?: string
    logo_url?: string
    privacy_policy_url?: string
    terms_of_service_url?: string
    gdpr_compliance?: boolean
    is_active?: boolean
}

export interface UpdateWidgetRequest extends Partial<CreateWidgetRequest> { }

// API Error Class
export class ApiError extends Error {
    constructor(
        message: string,
        public status: number,
        public errors?: Record<string, string[]>
    ) {
        super(message)
        this.name = 'ApiError'
    }
}

// Helper function to convert camelCase to snake_case
function toSnakeCase(obj: Record<string, any>): Record<string, any> {
    const result: Record<string, any> = {}

    for (const [key, value] of Object.entries(obj)) {
        const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`)
        result[snakeKey] = value
    }

    return result
}

// Helper function to convert snake_case to camelCase
function toCamelCase(obj: Record<string, any>): Record<string, any> {
    const result: Record<string, any> = {}

    for (const [key, value] of Object.entries(obj)) {
        const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
        result[camelKey] = value
    }

    return result
}

// API Client Class
class WidgetAPI {
    private baseURL: string
    private token: string | null = null

    constructor() {
        this.baseURL = API_BASE_URL
        // Get token from localStorage or your auth system
        if (typeof window !== 'undefined') {
            this.token = localStorage.getItem('auth_token')
        }
    }

    setToken(token: string) {
        this.token = token
        if (typeof window !== 'undefined') {
            localStorage.setItem('auth_token', token)
        }
    }

    private async request<T>(
        endpoint: string,
        options: RequestInit = {}
    ): Promise<ApiResponse<T>> {
        const url = `${this.baseURL}${endpoint}`

        const headers: HeadersInit = {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            ...options.headers,
        }

        if (this.token) {
            (headers as Record<string, string>).Authorization = `Bearer ${this.token}`
        }

        try {
            const response = await fetch(url, {
                ...options,
                headers,
            })

            const data = await response.json()

            if (!response.ok) {
                throw new ApiError(
                    data.message || 'An error occurred',
                    response.status,
                    data.errors
                )
            }

            return data
        } catch (error) {
            if (error instanceof ApiError) {
                throw error
            }

            throw new ApiError(
                error instanceof Error ? error.message : 'Network error',
                0
            )
        }
    }

    // Widget CRUD Operations
    async getWidgets(): Promise<Widget[]> {
        const response = await this.request<Widget[]>('/widgets')
        return response.data || []
    }

    async getWidget(id: number): Promise<Widget> {
        const response = await this.request<Widget>(`/widgets/${id}`)
        if (!response.data) {
            throw new ApiError('Widget not found', 404)
        }
        return response.data
    }

    async createWidget(data: CreateWidgetRequest): Promise<Widget> {
        // Convert camelCase to snake_case for API
        const snakeCaseData = toSnakeCase(data)

        const response = await this.request<Widget>('/widgets', {
            method: 'POST',
            body: JSON.stringify(snakeCaseData),
        })

        if (!response.data) {
            throw new ApiError('Failed to create widget', 500)
        }

        return response.data
    }

    async updateWidget(id: number, data: UpdateWidgetRequest): Promise<Widget> {
        // Convert camelCase to snake_case for API
        const snakeCaseData = toSnakeCase(data)

        const response = await this.request<Widget>(`/widgets/${id}`, {
            method: 'PUT',
            body: JSON.stringify(snakeCaseData),
        })

        if (!response.data) {
            throw new ApiError('Failed to update widget', 500)
        }

        return response.data
    }

    async deleteWidget(id: number): Promise<void> {
        await this.request(`/widgets/${id}`, {
            method: 'DELETE',
        })
    }

    // Public widget operations
    async getPublicWidget(slug: string): Promise<Widget> {
        const response = await this.request<Widget>(`/widgets/public/${slug}`)
        if (!response.data) {
            throw new ApiError('Widget not found', 404)
        }
        return response.data
    }

    async trackView(slug: string): Promise<void> {
        await this.request(`/widgets/track-view/${slug}`, {
            method: 'POST',
        })
    }

    async trackInteraction(slug: string): Promise<void> {
        await this.request(`/widgets/track-interaction/${slug}`, {
            method: 'POST',
        })
    }
}

// Export singleton instance
export const widgetAPI = new WidgetAPI()

// Export utility functions
export { toSnakeCase, toCamelCase } 