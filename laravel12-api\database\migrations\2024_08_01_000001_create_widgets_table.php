<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('widgets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('slug')->unique();

            // Appearance
            $table->string('primary_color')->default('#3b82f6');
            $table->string('secondary_color')->default('#64748b');
            $table->string('background_color')->default('#ffffff');
            $table->string('text_color')->default('#1f2937');
            $table->string('border_color')->default('#e5e7eb');
            $table->string('font_family')->default('inter');
            $table->string('font_size')->default('medium');
            $table->string('border_radius')->default('medium');
            $table->string('border_width')->default('1');
            $table->string('shadow_size')->default('medium');

            // Behavior
            $table->string('position')->default('bottom-right');
            $table->string('widget_size')->default('medium');
            $table->integer('margin_x')->default(20);
            $table->integer('margin_y')->default(20);
            $table->boolean('auto_open')->default(false);
            $table->boolean('show_on_mobile')->default(true);
            $table->boolean('allow_minimize')->default(true);
            $table->integer('auto_open_delay')->default(3);
            $table->boolean('sound_notifications')->default(false);
            $table->boolean('typing_indicator')->default(true);
            $table->boolean('message_timestamps')->default(true);
            $table->integer('response_delay')->default(1);
            $table->integer('z_index')->default(9999);
            $table->string('animation_style')->default('slide');

            // Content
            $table->string('welcome_title')->default('Hi there! 👋');
            $table->text('welcome_message')->default('How can I help you today?');
            $table->integer('welcome_delay')->default(2);
            $table->string('input_placeholder')->default('Type your message here...');
            $table->integer('max_message_length')->default(500);
            $table->string('chat_button_text')->default('Start Chat');
            $table->string('send_button_text')->default('Send');
            $table->string('close_button_text')->default('Close Chat');
            $table->string('minimize_button_text')->default('Minimize');
            $table->json('quick_replies')->nullable();
            $table->string('connection_error')->default('Unable to connect. Please check your internet connection.');
            $table->string('typing_message')->default('Assistant is typing...');
            $table->text('offline_message')->default('We are currently offline. Please leave a message and we will get back to you soon.');
            $table->string('company_name')->default('Acme Corp');
            $table->string('company_logo')->nullable();
            $table->string('agent_name')->default('AI Assistant');
            $table->string('agent_avatar')->nullable();
            $table->boolean('show_powered_by')->default(true);
            $table->string('powered_by_text')->default('Powered by ChatWidget Builder');
            $table->string('logo_url')->nullable();
            $table->string('privacy_policy_url')->nullable();
            $table->string('terms_of_service_url')->nullable();
            $table->boolean('gdpr_compliance')->default(false);

            // Additional fields for tracking
            $table->boolean('is_active')->default(true);
            $table->integer('views')->default(0);
            $table->integer('interactions')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('widgets');
    }
};
