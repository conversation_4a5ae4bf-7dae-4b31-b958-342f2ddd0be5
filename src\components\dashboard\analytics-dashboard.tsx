"use client"

import { useState } from "react"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { 
  BarChart3, 
  <PERSON><PERSON>hart, 
  Users, 
  MessageSquare, 
  Bot, 
  Zap, 
  ArrowRight, 
  Plus, 
  Settings,
  ChevronRight,
  Sparkles
} from "lucide-react"

interface AnalyticsDashboardProps {
  className?: string
}

export function AnalyticsDashboard({ className = "" }: AnalyticsDashboardProps) {
  const [activeTab, setActiveTab] = useState("overview")
  
  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header with gradient background */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-600 to-indigo-700 p-8 text-white shadow-lg">
        <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))]"></div>
        <div className="relative z-10">
          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Welcome to ChatWidget Builder</h1>
              <p className="mt-2 text-blue-100">
                Build, customize, and deploy AI chat widgets for your website
              </p>
            </div>
            <Button className="mt-4 sm:mt-0 bg-white text-blue-600 hover:bg-blue-50 shadow-md flex items-center gap-2 group">
              <span>Create New Widget</span>
              <Plus className="h-4 w-4 transition-transform group-hover:rotate-90" />
            </Button>
          </div>
          
          {/* Quick stats */}
          <div className="mt-8 grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-4">
            <div className="rounded-xl bg-white/10 backdrop-blur-sm p-4 shadow-sm border border-white/20">
              <div className="flex items-center gap-3">
                <div className="rounded-lg bg-white/20 p-2">
                  <Bot className="h-5 w-5" />
                </div>
                <div>
                  <p className="text-sm font-medium text-blue-100">Active Widgets</p>
                  <h3 className="text-2xl font-bold">3</h3>
                </div>
              </div>
            </div>
            
            <div className="rounded-xl bg-white/10 backdrop-blur-sm p-4 shadow-sm border border-white/20">
              <div className="flex items-center gap-3">
                <div className="rounded-lg bg-white/20 p-2">
                  <MessageSquare className="h-5 w-5" />
                </div>
                <div>
                  <p className="text-sm font-medium text-blue-100">Total Conversations</p>
                  <h3 className="text-2xl font-bold">128</h3>
                </div>
              </div>
            </div>
            
            <div className="rounded-xl bg-white/10 backdrop-blur-sm p-4 shadow-sm border border-white/20">
              <div className="flex items-center gap-3">
                <div className="rounded-lg bg-white/20 p-2">
                  <Users className="h-5 w-5" />
                </div>
                <div>
                  <p className="text-sm font-medium text-blue-100">Unique Visitors</p>
                  <h3 className="text-2xl font-bold">1,204</h3>
                </div>
              </div>
            </div>
            
            <div className="rounded-xl bg-white/10 backdrop-blur-sm p-4 shadow-sm border border-white/20">
              <div className="flex items-center gap-3">
                <div className="rounded-lg bg-white/20 p-2">
                  <Zap className="h-5 w-5" />
                </div>
                <div>
                  <p className="text-sm font-medium text-blue-100">Avg. Response Time</p>
                  <h3 className="text-2xl font-bold">1.2s</h3>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Decorative elements */}
        <div className="absolute top-0 right-0 -mt-16 -mr-16 h-64 w-64 rounded-full bg-blue-500/30 blur-3xl"></div>
        <div className="absolute bottom-0 left-0 -mb-16 -ml-16 h-64 w-64 rounded-full bg-indigo-500/30 blur-3xl"></div>
      </div>
      
      {/* Main content area */}
      <div className="space-y-6">
        <Tabs defaultValue="overview" className="w-full" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3 lg:w-auto lg:inline-flex">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              <span>Overview</span>
            </TabsTrigger>
            <TabsTrigger value="widgets" className="flex items-center gap-2">
              <Bot className="h-4 w-4" />
              <span>My Widgets</span>
            </TabsTrigger>
            <TabsTrigger value="providers" className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              <span>AI Providers</span>
            </TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-6 pt-6">
            {/* Activity chart card */}
            <Card className="p-6">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-lg font-semibold">Conversation Activity</h3>
                  <p className="text-sm text-muted-foreground">Last 30 days</p>
                </div>
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <span>View Details</span>
                  <ChevronRight className="h-3 w-3" />
                </Button>
              </div>
              
              {/* Placeholder for chart */}
              <div className="h-64 rounded-lg bg-slate-100 dark:bg-slate-800 flex items-center justify-center">
                <div className="text-center">
                  <BarChart3 className="h-10 w-10 mx-auto text-slate-400" />
                  <p className="mt-2 text-sm text-slate-500 dark:text-slate-400">
                    Activity visualization would appear here
                  </p>
                </div>
              </div>
            </Card>
            
            {/* Quick actions grid */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              <Card className="p-6 hover:shadow-md transition-all duration-200 group cursor-pointer border-2 border-transparent hover:border-blue-100 dark:hover:border-blue-900">
                <div className="flex items-start justify-between">
                  <div className="h-12 w-12 rounded-lg bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                    <Plus className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                  </div>
                  <ArrowRight className="h-5 w-5 text-slate-400 group-hover:text-blue-500 transition-all duration-200 group-hover:translate-x-1" />
                </div>
                <h3 className="mt-4 text-lg font-semibold">Create New Widget</h3>
                <p className="mt-1 text-sm text-slate-500 dark:text-slate-400">
                  Design and configure a new AI chat widget
                </p>
              </Card>
              
              <Card className="p-6 hover:shadow-md transition-all duration-200 group cursor-pointer border-2 border-transparent hover:border-purple-100 dark:hover:border-purple-900">
                <div className="flex items-start justify-between">
                  <div className="h-12 w-12 rounded-lg bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                    <Sparkles className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                  </div>
                  <ArrowRight className="h-5 w-5 text-slate-400 group-hover:text-purple-500 transition-all duration-200 group-hover:translate-x-1" />
                </div>
                <h3 className="mt-4 text-lg font-semibold">Connect AI Provider</h3>
                <p className="mt-1 text-sm text-slate-500 dark:text-slate-400">
                  Set up or manage your AI provider integrations
                </p>
              </Card>
              
              <Card className="p-6 hover:shadow-md transition-all duration-200 group cursor-pointer border-2 border-transparent hover:border-green-100 dark:hover:border-green-900">
                <div className="flex items-start justify-between">
                  <div className="h-12 w-12 rounded-lg bg-green-100 dark:bg-green-900 flex items-center justify-center">
                    <Settings className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <ArrowRight className="h-5 w-5 text-slate-400 group-hover:text-green-500 transition-all duration-200 group-hover:translate-x-1" />
                </div>
                <h3 className="mt-4 text-lg font-semibold">Account Settings</h3>
                <p className="mt-1 text-sm text-slate-500 dark:text-slate-400">
                  Manage your account preferences and billing
                </p>
              </Card>
            </div>
          </TabsContent>
          
          <TabsContent value="widgets" className="pt-6">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Your Chat Widgets</h3>
              <div className="space-y-4">
                {/* Widget list would go here */}
                <div className="text-center py-8">
                  <Bot className="h-12 w-12 mx-auto text-slate-300" />
                  <p className="mt-4 text-slate-500">No widgets created yet</p>
                  <Button className="mt-4" size="sm">Create Your First Widget</Button>
                </div>
              </div>
            </Card>
          </TabsContent>
          
          <TabsContent value="providers" className="pt-6">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">AI Provider Connections</h3>
              <div className="space-y-4">
                {/* Provider list would go here */}
                <div className="text-center py-8">
                  <Sparkles className="h-12 w-12 mx-auto text-slate-300" />
                  <p className="mt-4 text-slate-500">No AI providers configured</p>
                  <Button className="mt-4" size="sm">Connect an AI Provider</Button>
                </div>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
