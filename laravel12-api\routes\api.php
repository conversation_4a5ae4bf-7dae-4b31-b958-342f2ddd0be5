<?php

use App\Http\Controllers\WidgetController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Public widget routes
Route::get('/widgets/public/{slug}', [WidgetController::class, 'getPublicWidget']);
Route::post('/widgets/track-view/{slug}', [WidgetController::class, 'trackView']);
Route::post('/widgets/track-interaction/{slug}', [WidgetController::class, 'trackInteraction']);

 Route::apiResource('widgets', WidgetController::class);
