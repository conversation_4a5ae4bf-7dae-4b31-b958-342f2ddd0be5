<?php

namespace App\Services;

use App\Models\Widget;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class WidgetService
{
    /**
     * Get all widgets for a specific user.
     */
    public function getUserWidgets(int $userId): Collection
    {
        return Widget::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get a specific widget by ID.
     */
    public function getWidget(string $id): Widget
    {
        $widget = Widget::find($id);

        if (!$widget) {
            throw new ModelNotFoundException('Widget not found');
        }

        return $widget;
    }

    /**
     * Get a widget by its slug.
     */
    public function getWidgetBySlug(string $slug): Widget
    {
        $widget = Widget::where('slug', $slug)
            ->where('is_active', true)
            ->firstOrFail();

        return $widget;
    }

    /**
     * Create a new widget.
     */
    public function createWidget(array $data, int $userId): Widget
    {
        $widget = new Widget($data);
        $widget->user_id = $userId;
        $widget->save();

        return $widget;
    }

    /**
     * Update an existing widget.
     */
    public function updateWidget(string $id, array $data): Widget
    {
        $widget = $this->getWidget($id);
        $widget->update($data);

        return $widget;
    }

    /**
     * Delete a widget.
     */
    public function deleteWidget(string $id): bool
    {
        $widget = $this->getWidget($id);

        return $widget->delete();
    }

    /**
     * Increment the view count for a widget.
     */
    public function incrementViews(string $slug): void
    {
        Widget::where('slug', $slug)->increment('views');
    }

    /**
     * Increment the interaction count for a widget.
     */
    public function incrementInteractions(string $slug): void
    {
        Widget::where('slug', $slug)->increment('interactions');
    }
}
