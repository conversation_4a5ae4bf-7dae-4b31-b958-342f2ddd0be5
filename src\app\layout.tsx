import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";
import { ThemeProvider } from "@/components/theme-provider";
import { SidebarNavigation } from "@/components/layout/sidebar-navigation";
import { TopbarHeader } from "@/components/layout/topbar-header";
import { MainLayoutWrapper } from "@/components/layout/main-layout-wrapper";
import { Toaster } from "@/components/layout/toaster";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ChatWidget Builder",
  description: "Multi-AI chat widget builder SaaS platform",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <div className="flex h-screen bg-background">
            {/* Desktop Sidebar */}
            <aside className="hidden lg:flex lg:w-64 lg:flex-col lg:border-r">
              <div className="flex h-14 items-center border-b px-4">
                <h2 className="text-lg font-semibold">ChatWidget Builder</h2>
              </div>
              <div className="flex-1 overflow-auto p-4">
                <SidebarNavigation />
              </div>
            </aside>

            {/* Main Content Area */}
            <div className="flex flex-1 flex-col overflow-hidden">
              <TopbarHeader />
              <MainLayoutWrapper>
        {children}
              </MainLayoutWrapper>
            </div>
          </div>
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  );
}
