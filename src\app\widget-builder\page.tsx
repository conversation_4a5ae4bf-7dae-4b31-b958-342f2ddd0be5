"use client"

import { useState } from "react"
import { Toaster } from "sonner"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Loader2, Save, Eye, Settings, Palette, MessageSquare } from "lucide-react"

// Import existing components
import { WidgetPreview } from "@/components/widget/widget-preview"
import { AppearanceConfig } from "@/components/widget/appearance-config"
import { BehaviorConfig } from "@/components/widget/behavior-config"
import { ContentConfig } from "@/components/widget/content-config"

// Import new form components and hooks
import { FormField } from "@/components/ui/form-field"
import { useWidgetForm } from "@/hooks/use-widget-form"
import { Widget } from "@/lib/api"

export default function WidgetBuilderPage() {
    const [activeTab, setActiveTab] = useState("appearance")
    const [savedWidget, setSavedWidget] = useState<Widget | null>(null)

    const {
        formData,
        errors,
        isLoading,
        isValid,
        updateField,
        updateConfig,
        getFieldError,
        hasFieldError,
        createWidget,
        updateWidget
    } = useWidgetForm({
        onSuccess: (widget) => {
            setSavedWidget(widget)
        },
        onError: (error) => {
            console.error('Widget operation failed:', error)
        }
    })

    const handleSave = async () => {
        if (savedWidget) {
            await updateWidget(savedWidget.id)
        } else {
            await createWidget()
        }
    }

    const handleQuickReplyUpdate = (index: number, value: string) => {
        const newQuickReplies = [...formData.quickReplies]
        newQuickReplies[index] = value
        updateConfig({ quickReplies: newQuickReplies })
    }

    const handleQuickReplyAdd = (value: string) => {
        updateConfig({
            quickReplies: [...formData.quickReplies, value]
        })
    }

    const handleQuickReplyRemove = (index: number) => {
        const newQuickReplies = formData.quickReplies.filter((_, i) => i !== index)
        updateConfig({ quickReplies: newQuickReplies })
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
            <Toaster
                position="top-right"
                richColors
                closeButton
                toastOptions={{
                    duration: 4000,
                }}
            />

            {/* Main Container - Full width up to sidebar */}
            <div className="px-6 py-8 w-full">
                {/* Header Section - Standardized spacing */}
                <header className="mb-8">
                    <div className="flex items-start justify-between gap-6">
                        <div className="space-y-2">
                            <h1 className="text-3xl font-bold text-slate-900 dark:text-slate-100">
                                Widget Builder
                            </h1>
                            <p className="text-slate-600 dark:text-slate-400 text-base leading-relaxed">
                                Create and customize your chat widget with real-time preview
                            </p>
                        </div>

                        <div className="flex items-center gap-3 flex-shrink-0">
                            {savedWidget && (
                                <Badge variant="secondary" className="px-3 py-1.5 text-sm">
                                    Widget ID: {savedWidget.id}
                                </Badge>
                            )}

                            <Button
                                onClick={handleSave}
                                disabled={isLoading || !isValid}
                                size="lg"
                                className="bg-blue-600 hover:bg-blue-700 text-white shadow-lg"
                            >
                                {isLoading ? (
                                    <>
                                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                                        {savedWidget ? 'Updating...' : 'Creating...'}
                                    </>
                                ) : (
                                    <>
                                        <Save className="w-4 h-4 mr-2" />
                                        {savedWidget ? 'Update Widget' : 'Create Widget'}
                                    </>
                                )}
                            </Button>
                        </div>
                    </div>
                </header>

                {/* Main Content Grid - Professional layout */}
                <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
                    {/* Configuration Panel */}
                    <div className="space-y-6">
                        {/* Widget Identity Card - Premium design */}
                        <Card className="relative overflow-hidden border-0 shadow-xl bg-gradient-to-br from-white via-blue-50/30 to-indigo-50/50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-700">
                            {/* Decorative Elements */}
                            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/10 to-indigo-500/10 rounded-full -translate-y-16 translate-x-16" />
                            <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-purple-500/10 to-pink-500/10 rounded-full translate-y-12 -translate-x-12" />

                            <CardHeader className="relative z-10 pb-6">
                                <div className="flex items-start justify-between gap-4">
                                    <div className="flex items-center gap-4">
                                        <div className="p-3 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl shadow-lg">
                                            <Settings className="w-5 h-5 text-white" />
                                        </div>
                                        <div className="space-y-1">
                                            <CardTitle className="text-xl font-bold text-slate-900 dark:text-slate-100">
                                                Widget Identity
                                            </CardTitle>
                                            <CardDescription className="text-slate-600 dark:text-slate-400">
                                                Configure your widget's basic settings and identity
                                            </CardDescription>
                                        </div>
                                    </div>
                                    {savedWidget && (
                                        <Badge variant="secondary" className="bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-200 dark:border-green-800 flex-shrink-0">
                                            <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
                                            Active
                                        </Badge>
                                    )}
                                </div>
                            </CardHeader>

                            <CardContent className="relative z-10 space-y-6">
                                {/* Widget Name Input - Professional styling */}
                                <div className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <label htmlFor="widget-name" className="text-sm font-semibold text-slate-700 dark:text-slate-300 flex items-center gap-2">
                                            Widget Name
                                            <span className="text-red-500">*</span>
                                        </label>
                                        <span className="text-xs text-slate-500 dark:text-slate-400 font-medium">
                                            {formData.name.length}/100
                                        </span>
                                    </div>

                                    <div className="relative group">
                                        <input
                                            id="widget-name"
                                            type="text"
                                            value={formData.name}
                                            onChange={(e) => updateField('name', e.target.value)}
                                            placeholder="e.g., Customer Support Chat, Sales Assistant, Help Desk..."
                                            maxLength={100}
                                            className={`w-full h-12 px-4 pr-12 text-base font-medium bg-white dark:bg-slate-800 border-2 rounded-xl transition-all duration-300 shadow-sm hover:shadow-md focus:shadow-lg focus:scale-[1.01] ${hasFieldError('name')
                                                ? 'border-red-400 focus:border-red-500 focus:ring-4 focus:ring-red-100 dark:focus:ring-red-900'
                                                : 'border-slate-200 dark:border-slate-600 focus:border-blue-500 focus:ring-4 focus:ring-blue-100 dark:focus:ring-blue-900'
                                                } placeholder:text-slate-400 dark:placeholder:text-slate-500`}
                                        />

                                        {/* Status Indicator */}
                                        <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                                            {formData.name.length > 0 ? (
                                                hasFieldError('name') ? (
                                                    <div className="w-4 h-4 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                                                        <div className="w-2 h-2 bg-red-500 rounded-full" />
                                                    </div>
                                                ) : (
                                                    <div className="w-4 h-4 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                                                        <div className="w-2 h-2 bg-green-500 rounded-full" />
                                                    </div>
                                                )
                                            ) : (
                                                <div className="w-4 h-4 bg-slate-100 dark:bg-slate-700 rounded-full flex items-center justify-center">
                                                    <div className="w-2 h-2 bg-slate-400 rounded-full" />
                                                </div>
                                            )}
                                        </div>

                                        {/* Focus Effect */}
                                        <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/20 to-indigo-500/20 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 -z-10 blur-xl" />
                                    </div>

                                    {/* Error Message */}
                                    {hasFieldError('name') && (
                                        <div className="flex items-center gap-3 text-sm text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-950 px-4 py-3 rounded-lg border border-red-200 dark:border-red-800">
                                            <div className="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center flex-shrink-0">
                                                <div className="w-1.5 h-1.5 bg-white rounded-full" />
                                            </div>
                                            <span className="font-medium">{getFieldError('name')}</span>
                                        </div>
                                    )}

                                    {/* Help Text */}
                                    <div className="flex items-start gap-3 text-xs text-slate-500 dark:text-slate-400 bg-slate-50 dark:bg-slate-800/50 px-4 py-3 rounded-lg border border-slate-200 dark:border-slate-700">
                                        <div className="w-3 h-3 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                            <div className="w-1 h-1 bg-blue-500 rounded-full" />
                                        </div>
                                        <div className="space-y-1">
                                            <p className="font-medium text-slate-600 dark:text-slate-300">For your reference only</p>
                                            <p className="leading-relaxed">This name helps you identify the widget in your dashboard and won't be visible to your website visitors.</p>
                                        </div>
                                    </div>
                                </div>

                                {/* Widget URL Preview */}
                                {savedWidget && (
                                    <div className="space-y-3 pt-6 border-t border-slate-200 dark:border-slate-700">
                                        <label className="text-sm font-semibold text-slate-700 dark:text-slate-300">
                                            Widget URL
                                        </label>
                                        <div className="flex items-center gap-3 p-4 bg-slate-50 dark:bg-slate-800 rounded-xl border border-slate-200 dark:border-slate-700">
                                            <div className="flex-1 font-mono text-sm text-slate-600 dark:text-slate-400 truncate">
                                                {`${window.location.origin}/widget/${savedWidget.slug}`}
                                            </div>
                                            <Button
                                                size="sm"
                                                variant="outline"
                                                onClick={() => navigator.clipboard.writeText(`${window.location.origin}/widget/${savedWidget.slug}`)}
                                                className="text-xs font-medium"
                                            >
                                                Copy
                                            </Button>
                                        </div>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Configuration Tabs - Clean design */}
                        <Card className="shadow-lg border-0">
                            <CardContent className="p-0">
                                <Tabs value={activeTab} onValueChange={setActiveTab}>
                                    <div className="border-b border-slate-200 dark:border-slate-700">
                                        <TabsList className="grid w-full grid-cols-3 bg-transparent h-auto p-0 rounded-none">
                                            <TabsTrigger
                                                value="appearance"
                                                className="flex items-center gap-2 py-4 px-6 data-[state=active]:bg-slate-50 dark:data-[state=active]:bg-slate-800 rounded-none border-b-2 border-transparent data-[state=active]:border-blue-500"
                                            >
                                                <Palette className="w-4 h-4" />
                                                <span className="font-medium">Appearance</span>
                                            </TabsTrigger>
                                            <TabsTrigger
                                                value="behavior"
                                                className="flex items-center gap-2 py-4 px-6 data-[state=active]:bg-slate-50 dark:data-[state=active]:bg-slate-800 rounded-none border-b-2 border-transparent data-[state=active]:border-blue-500"
                                            >
                                                <Settings className="w-4 h-4" />
                                                <span className="font-medium">Behavior</span>
                                            </TabsTrigger>
                                            <TabsTrigger
                                                value="content"
                                                className="flex items-center gap-2 py-4 px-6 data-[state=active]:bg-slate-50 dark:data-[state=active]:bg-slate-800 rounded-none border-b-2 border-transparent data-[state=active]:border-blue-500"
                                            >
                                                <MessageSquare className="w-4 h-4" />
                                                <span className="font-medium">Content</span>
                                            </TabsTrigger>
                                        </TabsList>
                                    </div>

                                    <div className="p-6">
                                        <TabsContent value="appearance" className="mt-0 space-y-6">
                                            <AppearanceConfig
                                                config={formData}
                                                updateConfig={updateConfig}
                                            />
                                        </TabsContent>

                                        <TabsContent value="behavior" className="mt-0 space-y-6">
                                            <BehaviorConfig
                                                config={formData}
                                                updateConfig={updateConfig}
                                            />
                                        </TabsContent>

                                        <TabsContent value="content" className="mt-0 space-y-6">
                                            <ContentConfig
                                                config={formData}
                                                updateConfig={updateConfig}
                                                updateQuickReply={handleQuickReplyUpdate}
                                                addQuickReply={handleQuickReplyAdd}
                                                removeQuickReply={handleQuickReplyRemove}
                                            />
                                        </TabsContent>
                                    </div>
                                </Tabs>
                            </CardContent>
                        </Card>

                        {/* Validation Summary - Professional error display */}
                        {Object.keys(errors).length > 0 && (
                            <Card className="border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950 shadow-lg">
                                <CardHeader className="pb-4">
                                    <CardTitle className="text-red-800 dark:text-red-200 text-base font-semibold">
                                        Validation Errors
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <ul className="space-y-2 text-sm text-red-700 dark:text-red-300">
                                        {Object.entries(errors).map(([field, fieldErrors]) => (
                                            <li key={field} className="flex items-start gap-2">
                                                <div className="w-1.5 h-1.5 bg-red-500 rounded-full mt-2 flex-shrink-0" />
                                                <div>
                                                    <strong className="font-medium capitalize">{field}:</strong> {fieldErrors[0]}
                                                </div>
                                            </li>
                                        ))}
                                    </ul>
                                </CardContent>
                            </Card>
                        )}
                    </div>

                    {/* Preview Panel - Consistent spacing */}
                    <div className="space-y-6">
                        <Card className="shadow-lg border-0">
                            <CardHeader className="pb-4">
                                <CardTitle className="flex items-center gap-3 text-xl font-bold">
                                    <Eye className="w-5 h-5" />
                                    Live Preview
                                </CardTitle>
                                <CardDescription className="text-base">
                                    See how your widget will look on desktop and mobile devices
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="p-6">
                                <WidgetPreview config={formData} />
                            </CardContent>
                        </Card>

                        {/* Widget Statistics - Professional metrics display */}
                        {savedWidget && (
                            <Card className="shadow-lg border-0">
                                <CardHeader className="pb-4">
                                    <CardTitle className="text-base font-semibold">Widget Statistics</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-6">
                                    <div className="grid grid-cols-2 gap-4">
                                        <div className="text-center p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                                            <div className="text-muted-foreground text-sm font-medium mb-1">Views</div>
                                            <div className="text-2xl font-bold text-slate-900 dark:text-slate-100">{savedWidget.views}</div>
                                        </div>
                                        <div className="text-center p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                                            <div className="text-muted-foreground text-sm font-medium mb-1">Interactions</div>
                                            <div className="text-2xl font-bold text-slate-900 dark:text-slate-100">{savedWidget.interactions}</div>
                                        </div>
                                    </div>
                                    <div className="pt-4 border-t border-slate-200 dark:border-slate-700 space-y-2">
                                        <div className="text-xs text-muted-foreground">
                                            <span className="font-medium">Created:</span> {new Date(savedWidget.created_at).toLocaleDateString()}
                                        </div>
                                        <div className="text-xs text-muted-foreground">
                                            <span className="font-medium">Updated:</span> {new Date(savedWidget.updated_at).toLocaleDateString()}
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </div>
            </div>
        </div>
    )
} 