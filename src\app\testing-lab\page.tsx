"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { DeviceModeToggle } from "@/components/testing-lab/device-mode-toggle"
import { ChatPreviewFrame } from "@/components/testing-lab/chat-preview-frame"
import { MockChatMessages } from "@/components/testing-lab/mock-chat-messages"
import {
    Play,
    RotateCcw,
    Settings,
    Monitor,
    Tablet,
    Smartphone,
    Wifi,
    Battery,
    Signal,
    TestTube,
    Activity,
    Zap
} from "lucide-react"

export type DeviceMode = "desktop" | "tablet" | "mobile"

export default function TestingLabPage() {
    const [deviceMode, setDeviceMode] = useState<DeviceMode>("desktop")
    const [isSimulating, setIsSimulating] = useState(false)
    const [chatMessages, setChatMessages] = useState<Array<{
        id: string
        type: "user" | "bot"
        message: string
        timestamp: string
    }>>([])

    const handleStartSimulation = () => {
        setIsSimulating(true)
        setChatMessages([])

        // Simulate chat flow with delays
        setTimeout(() => {
            setChatMessages([{
                id: "1",
                type: "bot",
                message: "👋 Welcome! I'm here to help you today. How can I assist you?",
                timestamp: "Just now"
            }])
        }, 1000)

        setTimeout(() => {
            setChatMessages(prev => [...prev, {
                id: "2",
                type: "user",
                message: "Hi! I'm interested in learning more about your pricing plans.",
                timestamp: "Just now"
            }])
        }, 3000)

        setTimeout(() => {
            setChatMessages(prev => [...prev, {
                id: "3",
                type: "bot",
                message: "Great! I&apos;d be happy to explain our pricing options. We have three main plans: Starter, Professional, and Enterprise. Which would you like to know more about?",
                timestamp: "Just now"
            }])
        }, 5000)
    }

    const handleResetSimulation = () => {
        setIsSimulating(false)
        setChatMessages([])
    }

    const getDeviceIcon = (mode: DeviceMode) => {
        switch (mode) {
            case "desktop": return <Monitor className="h-4 w-4" />
            case "tablet": return <Tablet className="h-4 w-4" />
            case "mobile": return <Smartphone className="h-4 w-4" />
        }
    }

    const getDeviceDimensions = (mode: DeviceMode) => {
        switch (mode) {
            case "desktop": return "1920 × 1080"
            case "tablet": return "768 × 1024"
            case "mobile": return "375 × 812"
        }
    }

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
            {/* Hero Header Section */}
            <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white">
                <div className="absolute inset-0 bg-black/20"></div>
                <div className="absolute inset-0 opacity-30" style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                }}></div>
                <div className="relative px-6 py-16">
                    <div className="w-full">
                        <div className="flex items-center gap-4 mb-4">
                            <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                                <TestTube className="h-8 w-8" />
                            </div>
                            <div>
                                <h1 className="text-4xl font-bold mb-2">Testing Lab</h1>
                                <p className="text-blue-100 text-lg">
                                    Simulate and test your chat widget across different devices and environments
                                </p>
                            </div>
                        </div>

                        {/* Quick Stats */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
                            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold">{chatMessages.length}</div>
                                <div className="text-sm text-blue-100">Test Messages</div>
                            </div>
                            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold">{deviceMode.charAt(0).toUpperCase() + deviceMode.slice(1)}</div>
                                <div className="text-sm text-blue-100">Device Mode</div>
                            </div>
                            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold">{isSimulating ? "Active" : "Idle"}</div>
                                <div className="text-sm text-blue-100">Simulation</div>
                            </div>
                            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold">100%</div>
                                <div className="text-sm text-blue-100">Performance</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="w-full px-6 py-8">
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    {/* Control Panel */}
                    <div className="lg:col-span-1 space-y-6">
                        {/* Device Mode Toggle */}
                        <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-blue-50 dark:from-slate-900 dark:to-slate-800">
                            <CardHeader className="bg-gradient-to-r from-blue-500 to-indigo-500 text-white pb-4 pt-6 rounded-t-lg">
                                <CardTitle className="flex items-center gap-2">
                                    {getDeviceIcon(deviceMode)}
                                    Device Simulation
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="p-6">
                                <DeviceModeToggle
                                    deviceMode={deviceMode}
                                    onDeviceModeChange={setDeviceMode}
                                />
                                <Separator className="my-4" />
                                <div className="space-y-3 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">Resolution:</span>
                                        <span className="font-mono">{getDeviceDimensions(deviceMode)}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">Viewport:</span>
                                        <span className="font-mono">
                                            {deviceMode === "desktop" && "Desktop"}
                                            {deviceMode === "tablet" && "Tablet"}
                                            {deviceMode === "mobile" && "Mobile"}
                                        </span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">Scale:</span>
                                        <span className="font-mono">100%</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Simulation Controls */}
                        <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-green-50 dark:from-slate-900 dark:to-slate-800">
                            <CardHeader className="bg-gradient-to-r from-green-500 to-emerald-500 text-white pb-4 pt-6 rounded-t-lg">
                                <CardTitle className="flex items-center gap-2">
                                    <Activity className="h-5 w-5" />
                                    Simulation Controls
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="p-6">
                                <div className="space-y-3">
                                    <Button
                                        onClick={handleStartSimulation}
                                        disabled={isSimulating}
                                        className="w-full"
                                        size="sm"
                                    >
                                        <Play className="h-4 w-4 mr-2" />
                                        {isSimulating ? "Simulation Running..." : "Start Chat Flow"}
                                    </Button>
                                    <Button
                                        onClick={handleResetSimulation}
                                        variant="outline"
                                        className="w-full"
                                        size="sm"
                                    >
                                        <RotateCcw className="h-4 w-4 mr-2" />
                                        Reset Simulation
                                    </Button>
                                </div>
                                <Separator className="my-4" />
                                <div className="space-y-2 text-sm">
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">Messages:</span>
                                        <span>{chatMessages.length}</span>
                                    </div>
                                    <div className="flex justify-between">
                                        <span className="text-muted-foreground">Status:</span>
                                        <Badge variant={isSimulating ? "default" : "secondary"} className="text-xs">
                                            {isSimulating ? "Active" : "Idle"}
                                        </Badge>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Environment Info */}
                        <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-orange-50 dark:from-slate-900 dark:to-slate-800">
                            <CardHeader className="bg-gradient-to-r from-orange-500 to-red-500 text-white pb-4 pt-6 rounded-t-lg">
                                <CardTitle className="flex items-center gap-2">
                                    <Zap className="h-5 w-5" />
                                    Environment
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="p-6">
                                <div className="space-y-3 text-sm">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <Wifi className="h-4 w-4 text-green-500" />
                                            <span>Network</span>
                                        </div>
                                        <span className="text-green-600 font-medium">Online</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <Battery className="h-4 w-4 text-green-500" />
                                            <span>Performance</span>
                                        </div>
                                        <span className="text-green-600 font-medium">Optimal</span>
                                    </div>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            <Signal className="h-4 w-4 text-green-500" />
                                            <span>Connection</span>
                                        </div>
                                        <span className="text-green-600 font-medium">4G/WiFi</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Preview Canvas */}
                    <div className="lg:col-span-3">
                        <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-purple-50 dark:from-slate-900 dark:to-slate-800 h-full min-h-[700px]">
                            <CardHeader className="bg-gradient-to-r from-purple-500 to-pink-500 text-white pb-4 pt-6 rounded-t-lg">
                                <div className="flex items-center justify-between">
                                    <CardTitle className="flex items-center gap-2">
                                        {getDeviceIcon(deviceMode)}
                                        {deviceMode.charAt(0).toUpperCase() + deviceMode.slice(1)} Preview
                                    </CardTitle>
                                    <div className="flex items-center gap-2 text-sm text-purple-100">
                                        <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                        Live Render
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent className="p-6">
                                <ChatPreviewFrame deviceMode={deviceMode}>
                                    <MockChatMessages
                                        messages={chatMessages}
                                        isSimulating={isSimulating}
                                    />
                                </ChatPreviewFrame>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </div>
    )
} 