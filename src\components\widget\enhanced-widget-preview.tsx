"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { WidgetConfig } from "@/hooks/use-widget-config"
import { SharedWidgetPreview } from "./shared-widget-preview"
import { Monitor, Smartphone, Laptop, Tablet, RefreshCw, Code, Copy, Check, ExternalLink } from "lucide-react"

interface EnhancedWidgetPreviewProps {
  config: WidgetConfig
  className?: string
}

export function EnhancedWidgetPreview({ config, className = "" }: EnhancedWidgetPreviewProps) {
  const [activeDevice, setActiveDevice] = useState("desktop")
  const [isRotated, setIsRotated] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [isCopied, setIsCopied] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  
  // Simulate loading effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 1000)
    
    return () => clearTimeout(timer)
  }, [])
  
  // Handle refresh animation
  const handleRefresh = () => {
    setIsRefreshing(true)
    setIsLoading(true)
    
    setTimeout(() => {
      setIsRefreshing(false)
      setTimeout(() => {
        setIsLoading(false)
      }, 800)
    }, 500)
  }
  
  // Handle copy code
  const handleCopyCode = () => {
    setIsCopied(true)
    setTimeout(() => setIsCopied(false), 2000)
    // In a real implementation, this would copy the widget embed code
  }
  
  return (
    <div className={`w-full max-w-4xl mx-auto ${className}`}>
      <div className="flex flex-col space-y-6">
        {/* Enhanced Device Selection */}
        <div className="bg-white dark:bg-slate-900 rounded-2xl shadow-lg border border-slate-200 dark:border-slate-700 p-1">
          <div className="flex items-center justify-between p-2">
            <Tabs 
              defaultValue="desktop" 
              value={activeDevice}
              onValueChange={setActiveDevice}
              className="w-full"
            >
              <TabsList className="grid grid-cols-4 bg-slate-100 dark:bg-slate-800 rounded-xl p-1">
                <TabsTrigger
                  value="desktop"
                  className="flex items-center justify-center gap-1.5 rounded-lg py-2 px-3 text-sm font-medium transition-all duration-300 data-[state=active]:shadow-sm"
                >
                  <Monitor className="h-4 w-4" />
                  <span className="hidden sm:inline">Desktop</span>
                </TabsTrigger>
                <TabsTrigger
                  value="laptop"
                  className="flex items-center justify-center gap-1.5 rounded-lg py-2 px-3 text-sm font-medium transition-all duration-300 data-[state=active]:shadow-sm"
                >
                  <Laptop className="h-4 w-4" />
                  <span className="hidden sm:inline">Laptop</span>
                </TabsTrigger>
                <TabsTrigger
                  value="tablet"
                  className="flex items-center justify-center gap-1.5 rounded-lg py-2 px-3 text-sm font-medium transition-all duration-300 data-[state=active]:shadow-sm"
                >
                  <Tablet className="h-4 w-4" />
                  <span className="hidden sm:inline">Tablet</span>
                </TabsTrigger>
                <TabsTrigger
                  value="mobile"
                  className="flex items-center justify-center gap-1.5 rounded-lg py-2 px-3 text-sm font-medium transition-all duration-300 data-[state=active]:shadow-sm"
                >
                  <Smartphone className="h-4 w-4" />
                  <span className="hidden sm:inline">Mobile</span>
                </TabsTrigger>
              </TabsList>
            </Tabs>
            
            <div className="flex items-center gap-2 ml-2">
              {(activeDevice === "mobile" || activeDevice === "tablet") && (
                <Button
                  variant="outline"
                  size="sm"
                  className="h-9 px-2.5"
                  onClick={() => setIsRotated(!isRotated)}
                >
                  <div className={`transform transition-transform duration-300 ${isRotated ? "rotate-90" : ""}`}>
                    <Smartphone className="h-4 w-4" />
                  </div>
                </Button>
              )}
              
              <Button
                variant="outline"
                size="sm"
                className="h-9 px-2.5"
                onClick={handleRefresh}
              >
                <RefreshCw className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`} />
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                className="h-9 px-2.5"
                onClick={handleCopyCode}
              >
                {isCopied ? (
                  <Check className="h-4 w-4 text-green-500" />
                ) : (
                  <Code className="h-4 w-4" />
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Device Preview Area */}
        <div className="relative">
          {/* Loading overlay */}
          {isLoading && (
            <div className="absolute inset-0 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm z-10 flex items-center justify-center rounded-xl">
              <div className="flex flex-col items-center">
                <div className="w-10 h-10 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
                <p className="mt-4 text-sm text-slate-600 dark:text-slate-300">Loading preview...</p>
              </div>
            </div>
          )}
          
          {/* Desktop View */}
          {activeDevice === "desktop" && (
            <div className="relative bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-slate-800 dark:to-slate-900 rounded-xl p-8 min-h-[700px] overflow-visible shadow-lg">
              <div className="bg-white dark:bg-slate-800 rounded-xl shadow-2xl border border-slate-200 dark:border-slate-700 h-[600px] flex flex-col relative overflow-visible">
                {/* Browser Chrome */}
                <div className="flex items-center gap-3 p-4 border-b border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800 rounded-t-xl flex-shrink-0">
                  <div className="flex gap-2">
                    <div className="w-3 h-3 rounded-full bg-red-500 shadow-sm"></div>
                    <div className="w-3 h-3 rounded-full bg-yellow-500 shadow-sm"></div>
                    <div className="w-3 h-3 rounded-full bg-green-500 shadow-sm"></div>
                  </div>
                  <div className="flex-1 text-center">
                    <div className="bg-white dark:bg-slate-700 rounded-lg px-4 py-1.5 text-sm text-slate-600 dark:text-slate-300 border border-slate-200 dark:border-slate-600 max-w-md mx-auto flex items-center justify-center gap-2">
                      <div className="w-4 h-4 rounded-full bg-blue-500 flex items-center justify-center flex-shrink-0">
                        <ExternalLink className="h-2 w-2 text-white" />
                      </div>
                      <span>demo-website.com/widget-preview</span>
                    </div>
                  </div>
                </div>

                {/* Browser Content */}
                <div className="flex-1 relative overflow-visible bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 rounded-b-xl">
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="text-center max-w-md">
                      <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-4 mx-auto shadow-lg">
                        <Monitor className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="text-xl font-semibold text-slate-800 dark:text-slate-200 mb-2">Your Website</h3>
                      <p className="text-slate-500 dark:text-slate-400 mb-6">
                        This is how your AI chat widget will appear on your website
                      </p>
                      <div className="flex flex-wrap gap-2 justify-center">
                        <div className="w-24 h-6 bg-slate-200 dark:bg-slate-700 rounded-md animate-pulse"></div>
                        <div className="w-32 h-6 bg-slate-200 dark:bg-slate-700 rounded-md animate-pulse"></div>
                        <div className="w-20 h-6 bg-slate-200 dark:bg-slate-700 rounded-md animate-pulse"></div>
                      </div>
                    </div>
                  </div>
                  <div className="absolute bottom-4 right-4 z-10">
                    <SharedWidgetPreview config={config} isMobile={false} />
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Laptop View */}
          {activeDevice === "laptop" && (
            <div className="relative bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-slate-800 dark:to-slate-900 rounded-xl p-8 min-h-[600px] flex items-center justify-center shadow-lg">
              <div className="relative max-w-4xl mx-auto w-full">
                <div className="bg-slate-800 rounded-t-xl p-2 pt-1">
                  <div className="flex items-center justify-between px-2">
                    <div className="flex gap-1.5">
                      <div className="w-2.5 h-2.5 rounded-full bg-red-500"></div>
                      <div className="w-2.5 h-2.5 rounded-full bg-yellow-500"></div>
                      <div className="w-2.5 h-2.5 rounded-full bg-green-500"></div>
                    </div>
                    <div className="bg-slate-700 rounded-lg h-2 w-16 mx-auto"></div>
                    <div className="w-8"></div>
                  </div>
                </div>
                <div className="bg-slate-900 p-2 rounded-b-xl">
                  <div className="bg-white dark:bg-slate-800 aspect-[16/10] rounded-md overflow-hidden border border-slate-200 dark:border-slate-700 shadow-inner relative">
                    {/* Website Content */}
                    <div className="absolute inset-0 bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 flex items-center justify-center">
                      <div className="text-center max-w-md">
                        <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-4 mx-auto shadow-lg">
                          <Laptop className="h-8 w-8 text-white" />
                        </div>
                        <h3 className="text-xl font-semibold text-slate-800 dark:text-slate-200 mb-2">Widget Preview</h3>
                        <p className="text-slate-500 dark:text-slate-400 mb-4">
                          This is how your widget will appear on laptop screens
                        </p>
                      </div>
                    </div>
                    <div className="absolute bottom-6 right-6 z-10">
                      <SharedWidgetPreview config={config} isMobile={false} className="origin-bottom-right" />
                    </div>
                  </div>
                </div>
                <div className="bg-slate-800 h-2 w-full rounded-b-xl"></div>
                <div className="bg-slate-700 h-8 w-40 mx-auto rounded-b-lg"></div>
              </div>
            </div>
          )}
          
          {/* Tablet View */}
          {activeDevice === "tablet" && (
            <div className="relative bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-slate-800 dark:to-slate-900 rounded-xl p-8 min-h-[600px] flex items-center justify-center shadow-lg">
              <div className="relative">
                <div className={`bg-slate-800 rounded-3xl p-4 shadow-xl ${isRotated ? "w-[600px]" : "w-[450px]"}`}>
                  <div className={`bg-white dark:bg-slate-900 ${isRotated ? "aspect-[4/3]" : "aspect-[3/4]"} rounded-2xl overflow-hidden border border-slate-200 dark:border-slate-700 shadow-inner relative`}>
                    {/* Website Content */}
                    <div className="absolute inset-0 bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900 flex items-center justify-center">
                      <div className="text-center max-w-md">
                        <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center mb-3 mx-auto shadow-lg">
                          <Tablet className="h-6 w-6 text-white" />
                        </div>
                        <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-2">Tablet View</h3>
                        <p className="text-sm text-slate-500 dark:text-slate-400">
                          {isRotated ? "Landscape orientation" : "Portrait orientation"}
                        </p>
                      </div>
                    </div>
                    <div className="absolute bottom-3 right-3">
                      <SharedWidgetPreview config={config} isMobile={false} className="transform scale-75" />
                    </div>
                  </div>
                  <div className="flex justify-center mt-2">
                    <div className="w-8 h-8 rounded-full border-2 border-slate-600 bg-slate-700"></div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          {/* Mobile View */}
          {activeDevice === "mobile" && (
            <div className="relative bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-slate-800 dark:to-slate-900 rounded-xl p-8 min-h-[600px] flex items-center justify-center shadow-lg">
              <div className="relative">
                <div className={`w-[320px] h-[640px] ${isRotated ? "rotate-90 scale-75" : ""} transition-transform duration-500 bg-black rounded-[2.5rem] p-2 shadow-2xl`}>
                  <div className="w-full h-full bg-white dark:bg-slate-900 rounded-[2rem] overflow-hidden relative">
                    {/* Mobile Status Bar */}
                    <div className="bg-slate-50 dark:bg-slate-800 px-6 py-2 flex justify-between items-center text-xs font-medium">
                      <span>9:41</span>
                      <div className="flex items-center gap-1">
                        <div className="w-4 h-2 border border-slate-400 rounded-sm">
                          <div className="w-3 h-1 bg-green-500 rounded-sm m-0.5"></div>
                        </div>
                      </div>
                    </div>

                    <div className="flex-1 relative h-[580px] overflow-hidden">
                      <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-white to-slate-50 dark:from-slate-800 dark:to-slate-900">
                        <div className="text-center">
                          <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mb-3 mx-auto shadow-lg">
                            <Smartphone className="h-6 w-6 text-white" />
                          </div>
                          <h3 className="text-lg font-semibold text-slate-800 dark:text-slate-200 mb-2">Mobile Website</h3>
                          <p className="text-sm text-slate-500 dark:text-slate-400">
                            Widget preview on mobile
                          </p>
                        </div>
                      </div>
                      <div className="absolute bottom-3 right-3">
                        <SharedWidgetPreview config={config} isMobile={true} />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
        
        {/* Widget Code Preview */}
        <div className="bg-slate-900 rounded-xl p-4 text-slate-300 text-sm font-mono overflow-hidden">
          <div className="flex items-center justify-between mb-2">
            <div className="text-xs text-slate-500">widget-embed-code.js</div>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 text-slate-400 hover:text-white"
              onClick={handleCopyCode}
            >
              {isCopied ? (
                <div className="flex items-center gap-1 text-green-400">
                  <Check className="h-3.5 w-3.5" />
                  <span className="text-xs">Copied!</span>
                </div>
              ) : (
                <div className="flex items-center gap-1">
                  <Copy className="h-3.5 w-3.5" />
                  <span className="text-xs">Copy code</span>
                </div>
              )}
            </Button>
          </div>
          <div className="space-y-1">
            <div><span className="text-blue-400">&lt;script</span> <span className="text-green-400">src=</span><span className="text-amber-300">"https://cdn.chatwidget.com/embed.js"</span> <span className="text-green-400">defer</span><span className="text-blue-400">&gt;&lt;/script&gt;</span></div>
            <div><span className="text-blue-400">&lt;script&gt;</span></div>
            <div>  <span className="text-purple-400">window</span>.<span className="text-blue-300">chatWidget</span> = {`{`}</div>
            <div>    <span className="text-green-300">apiKey</span>: <span className="text-amber-300">"YOUR_API_KEY"</span>,</div>
            <div>    <span className="text-green-300">widgetId</span>: <span className="text-amber-300">"widget_12345"</span>,</div>
            <div>    <span className="text-green-300">primaryColor</span>: <span className="text-amber-300">"{config.primaryColor}"</span></div>
            <div>  {`}`};</div>
            <div><span className="text-blue-400">&lt;/script&gt;</span></div>
          </div>
        </div>
      </div>
    </div>
  )
}
