"use client"

import { <PERSON>actNode } from "react"
import { cn } from "@/lib/utils"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { AlertCircle } from "lucide-react"

interface BaseFormFieldProps {
    label: string
    id: string
    error?: string
    description?: string
    required?: boolean
    className?: string
}

interface InputFormFieldProps extends BaseFormFieldProps {
    type: "input"
    value: string | number
    onChange: (value: string) => void
    placeholder?: string
    inputType?: "text" | "number" | "email" | "url"
    min?: number
    max?: number
}

interface TextareaFormFieldProps extends BaseFormFieldProps {
    type: "textarea"
    value: string
    onChange: (value: string) => void
    placeholder?: string
    rows?: number
}

interface SelectFormFieldProps extends BaseFormFieldProps {
    type: "select"
    value: string
    onChange: (value: string) => void
    options: { value: string; label: string }[]
    placeholder?: string
}

interface SwitchFormFieldProps extends BaseFormFieldProps {
    type: "switch"
    checked: boolean
    onChange: (checked: boolean) => void
}

interface ColorFormFieldProps extends BaseFormFieldProps {
    type: "color"
    value: string
    onChange: (value: string) => void
}

type FormFieldProps =
    | InputFormFieldProps
    | TextareaFormFieldProps
    | SelectFormFieldProps
    | SwitchFormFieldProps
    | ColorFormFieldProps

export function FormField(props: FormFieldProps) {
    const { label, id, error, description, required, className } = props
    const hasError = Boolean(error)

    const renderField = () => {
        switch (props.type) {
            case "input":
                return (
                    <Input
                        id={id}
                        type={props.inputType || "text"}
                        value={props.value}
                        onChange={(e) => props.onChange(e.target.value)}
                        placeholder={props.placeholder}
                        min={props.min}
                        max={props.max}
                        className={cn(
                            hasError && "border-red-500 focus:border-red-500 focus:ring-red-500"
                        )}
                        aria-invalid={hasError}
                        aria-describedby={error ? `${id}-error` : undefined}
                    />
                )

            case "textarea":
                return (
                    <Textarea
                        id={id}
                        value={props.value}
                        onChange={(e) => props.onChange(e.target.value)}
                        placeholder={props.placeholder}
                        rows={props.rows || 3}
                        className={cn(
                            hasError && "border-red-500 focus:border-red-500 focus:ring-red-500"
                        )}
                        aria-invalid={hasError}
                        aria-describedby={error ? `${id}-error` : undefined}
                    />
                )

            case "select":
                return (
                    <Select value={props.value} onValueChange={props.onChange}>
                        <SelectTrigger
                            id={id}
                            className={cn(
                                hasError && "border-red-500 focus:border-red-500 focus:ring-red-500"
                            )}
                            aria-invalid={hasError}
                            aria-describedby={error ? `${id}-error` : undefined}
                        >
                            <SelectValue placeholder={props.placeholder} />
                        </SelectTrigger>
                        <SelectContent>
                            {props.options.map((option) => (
                                <SelectItem key={option.value} value={option.value}>
                                    {option.label}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                )

            case "switch":
                return (
                    <Switch
                        id={id}
                        checked={props.checked}
                        onCheckedChange={props.onChange}
                        aria-invalid={hasError}
                        aria-describedby={error ? `${id}-error` : undefined}
                    />
                )

            case "color":
                return (
                    <div className="flex gap-2">
                        <Input
                            id={id}
                            type="color"
                            value={props.value}
                            onChange={(e) => props.onChange(e.target.value)}
                            className={cn(
                                "w-16 h-10 p-1 rounded cursor-pointer",
                                hasError && "border-red-500 focus:border-red-500 focus:ring-red-500"
                            )}
                            aria-invalid={hasError}
                            aria-describedby={error ? `${id}-error` : undefined}
                        />
                        <Input
                            type="text"
                            value={props.value}
                            onChange={(e) => props.onChange(e.target.value)}
                            placeholder="#000000"
                            className={cn(
                                "flex-1",
                                hasError && "border-red-500 focus:border-red-500 focus:ring-red-500"
                            )}
                        />
                    </div>
                )

            default:
                return null
        }
    }

    return (
        <div className={cn("space-y-2", className)}>
            <div className="flex items-center gap-2">
                <Label
                    htmlFor={id}
                    className={cn(
                        "text-sm font-medium",
                        hasError && "text-red-700 dark:text-red-400"
                    )}
                >
                    {label}
                    {required && <span className="text-red-500 ml-1">*</span>}
                </Label>
            </div>

            {description && (
                <p className="text-sm text-muted-foreground">
                    {description}
                </p>
            )}

            {renderField()}

            {error && (
                <div
                    id={`${id}-error`}
                    className="flex items-center gap-2 text-sm text-red-600 dark:text-red-400"
                    role="alert"
                >
                    <AlertCircle className="h-4 w-4 flex-shrink-0" />
                    <span>{error}</span>
                </div>
            )}
        </div>
    )
}

// Specialized components for common use cases
interface QuickRepliesFieldProps {
    label: string
    id: string
    values: string[]
    onChange: (values: string[]) => void
    onAdd: (value: string) => void
    onRemove: (index: number) => void
    error?: string
    maxItems?: number
    maxLength?: number
}

export function QuickRepliesField({
    label,
    id,
    values,
    onChange,
    onAdd,
    onRemove,
    error,
    maxItems = 10,
    maxLength = 100
}: QuickRepliesFieldProps) {
    const hasError = Boolean(error)

    return (
        <div className="space-y-2">
            <Label
                htmlFor={id}
                className={cn(
                    "text-sm font-medium",
                    hasError && "text-red-700 dark:text-red-400"
                )}
            >
                {label}
            </Label>

            <div className="space-y-2">
                {values.map((value, index) => (
                    <div key={index} className="flex gap-2">
                        <Input
                            value={value}
                            onChange={(e) => {
                                const newValues = [...values]
                                newValues[index] = e.target.value
                                onChange(newValues)
                            }}
                            placeholder={`Quick reply ${index + 1}`}
                            maxLength={maxLength}
                            className={cn(
                                "flex-1",
                                hasError && "border-red-500 focus:border-red-500 focus:ring-red-500"
                            )}
                        />
                        <button
                            type="button"
                            onClick={() => onRemove(index)}
                            className="px-3 py-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md transition-colors"
                            aria-label={`Remove quick reply ${index + 1}`}
                        >
                            ×
                        </button>
                    </div>
                ))}

                {values.length < maxItems && (
                    <button
                        type="button"
                        onClick={() => onAdd("")}
                        className="w-full px-3 py-2 border border-dashed border-gray-300 rounded-md text-sm text-gray-600 hover:border-gray-400 hover:text-gray-800 transition-colors"
                    >
                        + Add Quick Reply
                    </button>
                )}
            </div>

            {error && (
                <div
                    className="flex items-center gap-2 text-sm text-red-600 dark:text-red-400"
                    role="alert"
                >
                    <AlertCircle className="h-4 w-4 flex-shrink-0" />
                    <span>{error}</span>
                </div>
            )}
        </div>
    )
} 