"use client"

import { <PERSON>u } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ThemeToggle } from "./theme-toggle"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { SidebarNavigation } from "./sidebar-navigation"
import { usePageTitle } from "@/hooks/use-page-title"

export function TopbarHeader() {
    const pageTitle = usePageTitle()

    return (
        <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="flex h-14 items-center justify-between px-4 lg:px-6">
                <div className="flex items-center gap-4">
                    {/* Mobile menu trigger */}
                    <Sheet>
                        <SheetTrigger asChild>
                            <Button variant="ghost" size="icon" className="lg:hidden">
                                <Menu className="h-5 w-5" />
                                <span className="sr-only">Toggle navigation menu</span>
                            </Button>
                        </SheetTrigger>
                        <SheetContent side="left" className="w-64 p-0">
                            <div className="flex h-full flex-col">
                                <div className="flex h-14 items-center border-b px-4">
                                    <h2 className="text-lg font-semibold">ChatWidget Builder</h2>
                                </div>
                                <div className="flex-1 overflow-auto p-4">
                                    <SidebarNavigation />
                                </div>
                            </div>
                        </SheetContent>
                    </Sheet>

                    {/* Page title */}
                    <h1 className="text-lg font-semibold">{pageTitle}</h1>
                </div>

                {/* Right side actions */}
                <div className="flex items-center gap-2">
                    <ThemeToggle />
                    <Avatar className="h-8 w-8">
                        <AvatarImage src="/placeholder-avatar.jpg" alt="User" />
                        <AvatarFallback>U</AvatarFallback>
                    </Avatar>
                </div>
            </div>
        </header>
    )
} 