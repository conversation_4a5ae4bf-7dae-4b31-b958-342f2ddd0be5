"use client"

import { useState } from "react"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { EmbedCodeBlock } from "@/components/embed/embed-code-block"
import { EmbedCodePreview } from "@/components/embed/embed-code-preview"
import { EmbedFormatSelector } from "@/components/embed/embed-format-selector"
import { Badge } from "@/components/ui/badge"
import { Settings, Code, Eye, Code2, Globe, FileCode } from "lucide-react"

export function EmbedCodePanel() {
    const [selectedFormat, setSelectedFormat] = useState("html")
    const [widgetId] = useState("widget_12345")
    const [apiKey] = useState("pk_live_abcd1234efgh5678")

    return (
        <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800">
            {/* Hero Header Section */}
            <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white">
                <div className="absolute inset-0 bg-black/20"></div>
                <div className="absolute inset-0 opacity-30" style={{
                    backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
                }}></div>
                <div className="relative px-6 py-16">
                    <div className="w-full">
                        <div className="flex items-center gap-4 mb-4">
                            <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                                <Code2 className="h-8 w-8" />
                            </div>
                            <div>
                                <h1 className="text-4xl font-bold mb-2">Embed Code Generator</h1>
                                <p className="text-blue-100 text-lg">
                                    Generate and preview your chat widget integration code
                                </p>
                            </div>
                        </div>

                        {/* Quick Stats */}
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
                            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold">3</div>
                                <div className="text-sm text-blue-100">Integration Methods</div>
                            </div>
                            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold">Live</div>
                                <div className="text-sm text-blue-100">Widget Status</div>
                            </div>
                            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold">Auto</div>
                                <div className="text-sm text-blue-100">Theme Mode</div>
                            </div>
                            <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4 text-center">
                                <div className="text-2xl font-bold">GPT-4</div>
                                <div className="text-sm text-blue-100">AI Model</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="w-full px-6 py-8">
                <div className="space-y-8">
                    {/* Configuration Header */}
                    <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-blue-50 dark:from-slate-900 dark:to-slate-800">
                        <CardHeader className="bg-gradient-to-r from-blue-500 to-purple-500 text-white pb-4 pt-6 rounded-t-lg">
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle className="flex items-center gap-2">
                                        <Settings className="h-5 w-5" />
                                        Widget Configuration
                                    </CardTitle>
                                    <CardDescription className="text-blue-100">
                                        Current widget settings for embed code generation
                                    </CardDescription>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Badge variant="secondary" className="bg-white/20 text-white border-white/30">Widget ID: {widgetId}</Badge>
                                    <Badge variant="outline" className="bg-green-500/20 text-green-100 border-green-300">Live</Badge>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent className="p-6">
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                <div>
                                    <span className="font-medium text-muted-foreground">Theme:</span>
                                    <p>Auto (Light/Dark)</p>
                                </div>
                                <div>
                                    <span className="font-medium text-muted-foreground">Position:</span>
                                    <p>Bottom Right</p>
                                </div>
                                <div>
                                    <span className="font-medium text-muted-foreground">AI Provider:</span>
                                    <p>OpenAI GPT-4</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Format Selector */}
                    <EmbedFormatSelector
                        selectedFormat={selectedFormat}
                        onFormatChange={setSelectedFormat}
                    />

                    {/* Main Content - Two Column Layout */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        {/* Left Column - Integration Code */}
                        <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-green-50 dark:from-slate-900 dark:to-slate-800 h-fit">
                            <CardHeader className="bg-gradient-to-r from-green-500 to-emerald-500 text-white pb-4 pt-6 rounded-t-lg">
                                <CardTitle className="flex items-center gap-2">
                                    <Code className="h-5 w-5" />
                                    Integration Code
                                </CardTitle>
                                <CardDescription className="text-green-100">
                                    Copy and paste this code into your website
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="p-6">
                                <EmbedCodeBlock
                                    format={selectedFormat}
                                    widgetId={widgetId}
                                    apiKey={apiKey}
                                />
                            </CardContent>
                        </Card>

                        {/* Right Column - Live Preview */}
                        <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-purple-50 dark:from-slate-900 dark:to-slate-800 h-fit">
                            <CardHeader className="bg-gradient-to-r from-purple-500 to-pink-500 text-white pb-4 pt-6 rounded-t-lg">
                                <CardTitle className="flex items-center gap-2">
                                    <Eye className="h-5 w-5" />
                                    Live Preview
                                </CardTitle>
                                <CardDescription className="text-purple-100">
                                    See how your widget will appear on your website
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="p-6">
                                <EmbedCodePreview
                                    widgetId={widgetId}
                                    format={selectedFormat}
                                />
                            </CardContent>
                        </Card>
                    </div>

                    {/* Implementation Guide */}
                    <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-indigo-50 dark:from-slate-900 dark:to-slate-800">
                        <CardHeader className="bg-gradient-to-r from-indigo-500 to-blue-500 text-white pb-4 pt-6 rounded-t-lg">
                            <CardTitle>Implementation Guide</CardTitle>
                            <CardDescription className="text-indigo-100">
                                Step-by-step instructions for integrating your chat widget
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="p-6">
                            <Tabs defaultValue="html" className="w-full">
                                <div className="bg-white dark:bg-slate-900 rounded-2xl shadow-lg border border-slate-200 dark:border-slate-700 p-1 mb-6">
                                    <TabsList className="grid w-full grid-cols-3 bg-transparent gap-1 h-auto p-1">
                                        <TabsTrigger
                                            value="html"
                                            className="flex items-center justify-center gap-2 rounded-lg py-2.5 px-3 text-sm font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white data-[state=active]:hover:from-blue-600 data-[state=active]:hover:to-purple-600"
                                        >
                                            <Globe className="h-4 w-4" />
                                            <span>HTML/JavaScript</span>
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="react"
                                            className="flex items-center justify-center gap-2 rounded-lg py-2.5 px-3 text-sm font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white data-[state=active]:hover:from-blue-600 data-[state=active]:hover:to-purple-600"
                                        >
                                            <Code2 className="h-4 w-4" />
                                            <span>React</span>
                                        </TabsTrigger>
                                        <TabsTrigger
                                            value="iframe"
                                            className="flex items-center justify-center gap-2 rounded-lg py-2.5 px-3 text-sm font-medium transition-all duration-200 data-[state=active]:shadow-sm hover:bg-slate-100 dark:hover:bg-slate-800 data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-purple-500 data-[state=active]:text-white data-[state=active]:hover:from-blue-600 data-[state=active]:hover:to-purple-600"
                                        >
                                            <FileCode className="h-4 w-4" />
                                            <span>iFrame</span>
                                        </TabsTrigger>
                                    </TabsList>
                                </div>

                                <TabsContent value="html" className="space-y-4">
                                    <div className="space-y-3">
                                        <div className="flex items-start gap-3">
                                            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm flex items-center justify-center font-medium">1</div>
                                            <div>
                                                <h4 className="font-medium">Add the script tag</h4>
                                                <p className="text-sm text-muted-foreground">Copy the script tag and paste it before the closing &lt;/body&gt; tag of your HTML</p>
                                            </div>
                                        </div>
                                        <div className="flex items-start gap-3">
                                            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm flex items-center justify-center font-medium">2</div>
                                            <div>
                                                <h4 className="font-medium">Initialize the widget</h4>
                                                <p className="text-sm text-muted-foreground">The widget will automatically initialize when the page loads</p>
                                            </div>
                                        </div>
                                        <div className="flex items-start gap-3">
                                            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm flex items-center justify-center font-medium">3</div>
                                            <div>
                                                <h4 className="font-medium">Test the integration</h4>
                                                <p className="text-sm text-muted-foreground">Refresh your page to see the chat widget in action</p>
                                            </div>
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="react" className="space-y-4">
                                    <div className="space-y-3">
                                        <div className="flex items-start gap-3">
                                            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm flex items-center justify-center font-medium">1</div>
                                            <div>
                                                <h4 className="font-medium">Install the React component</h4>
                                                <p className="text-sm text-muted-foreground">npm install @your-company/chat-widget-react</p>
                                            </div>
                                        </div>
                                        <div className="flex items-start gap-3">
                                            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm flex items-center justify-center font-medium">2</div>
                                            <div>
                                                <h4 className="font-medium">Import and use the component</h4>
                                                <p className="text-sm text-muted-foreground">Add the ChatWidget component to your React application</p>
                                            </div>
                                        </div>
                                        <div className="flex items-start gap-3">
                                            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm flex items-center justify-center font-medium">3</div>
                                            <div>
                                                <h4 className="font-medium">Configure props</h4>
                                                <p className="text-sm text-muted-foreground">Pass your widget ID and any custom configuration</p>
                                            </div>
                                        </div>
                                    </div>
                                </TabsContent>

                                <TabsContent value="iframe" className="space-y-4">
                                    <div className="space-y-3">
                                        <div className="flex items-start gap-3">
                                            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm flex items-center justify-center font-medium">1</div>
                                            <div>
                                                <h4 className="font-medium">Add the iframe</h4>
                                                <p className="text-sm text-muted-foreground">Copy the iframe code and paste it where you want the widget to appear</p>
                                            </div>
                                        </div>
                                        <div className="flex items-start gap-3">
                                            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm flex items-center justify-center font-medium">2</div>
                                            <div>
                                                <h4 className="font-medium">Customize dimensions</h4>
                                                <p className="text-sm text-muted-foreground">Adjust width and height attributes as needed</p>
                                            </div>
                                        </div>
                                        <div className="flex items-start gap-3">
                                            <div className="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 text-white text-sm flex items-center justify-center font-medium">3</div>
                                            <div>
                                                <h4 className="font-medium">Style the container</h4>
                                                <p className="text-sm text-muted-foreground">Apply CSS to position and style the iframe container</p>
                                            </div>
                                        </div>
                                    </div>
                                </TabsContent>
                            </Tabs>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </div>
    )
} 