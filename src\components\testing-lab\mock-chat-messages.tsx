"use client"

import { useEffect, useState } from "react"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, User, Clock } from "lucide-react"

interface Message {
    id: string
    type: "user" | "bot"
    message: string
    timestamp: string
}

interface MockChatMessagesProps {
    messages: Message[]
    isSimulating: boolean
}

export function MockChatMessages({ messages, isSimulating }: MockChatMessagesProps) {
    const [visibleMessages, setVisibleMessages] = useState<Message[]>([])
    const [typingIndicator, setTypingIndicator] = useState(false)

    useEffect(() => {
        if (messages.length === 0) {
            setVisibleMessages([])
            setTypingIndicator(false)
            return
        }

        // Animate messages appearing one by one
        messages.forEach((message, index) => {
            setTimeout(() => {
                setVisibleMessages(prev => {
                    if (!prev.find(m => m.id === message.id)) {
                        return [...prev, message]
                    }
                    return prev
                })
            }, index * 1000)
        })

        // Show typing indicator before bot messages
        messages.forEach((message, index) => {
            if (message.type === "bot" && index > 0) {
                setTimeout(() => {
                    setTypingIndicator(true)
                }, (index - 1) * 1000 + 500)

                setTimeout(() => {
                    setTypingIndicator(false)
                }, index * 1000)
            }
        })
    }, [messages])

    if (!isSimulating && messages.length === 0) {
        return (
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                <Card className="p-6 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm border-dashed border-2 border-slate-300 dark:border-slate-600 max-w-md">
                    <div className="text-center space-y-3">
                        <div className="w-12 h-12 bg-slate-100 dark:bg-slate-700 rounded-full flex items-center justify-center mx-auto">
                            <Bot className="h-6 w-6 text-slate-500" />
                        </div>
                        <div>
                            <h3 className="font-semibold text-slate-800 dark:text-slate-200 mb-1">
                                Ready for Testing
                            </h3>
                            <p className="text-sm text-slate-600 dark:text-slate-400">
                                Click "Start Chat Flow" to simulate user interactions
                            </p>
                        </div>
                    </div>
                </Card>
            </div>
        )
    }

    return (
        <div className="absolute inset-0 flex items-end justify-end p-6 pointer-events-none">
            <div className="space-y-4 max-w-sm">
                {/* Simulation Status */}
                {isSimulating && (
                    <div className="flex justify-center mb-4">
                        <Badge variant="secondary" className="bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-300 border-blue-200 dark:border-blue-700">
                            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse mr-2"></div>
                            Simulation Active
                        </Badge>
                    </div>
                )}

                {/* Messages */}
                {visibleMessages.map((message, index) => (
                    <div
                        key={message.id}
                        className={`flex gap-3 animate-fade-in ${message.type === "user" ? "justify-end" : "justify-start"
                            }`}
                        style={{ animationDelay: `${index * 0.2}s` }}
                    >
                        {message.type === "bot" && (
                            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center flex-shrink-0 shadow-lg">
                                <Bot className="h-4 w-4 text-white" />
                            </div>
                        )}

                        <div className="flex-1 min-w-0">
                            <div
                                className={`rounded-2xl p-4 shadow-lg max-w-[280px] ${message.type === "user"
                                    ? "bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-tr-md ml-auto"
                                    : "bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700 rounded-tl-md"
                                    }`}
                            >
                                <div className={`text-sm leading-relaxed ${message.type === "user"
                                    ? "text-white"
                                    : "text-slate-600 dark:text-slate-300"
                                    }`}>
                                    {message.message}
                                </div>
                            </div>

                            <div className="flex items-center gap-2 mt-2 text-xs text-slate-400">
                                <Clock className="h-3 w-3" />
                                <span>{message.timestamp}</span>
                            </div>
                        </div>

                        {message.type === "user" && (
                            <div className="w-8 h-8 rounded-full bg-gradient-to-br from-slate-400 to-slate-600 flex items-center justify-center flex-shrink-0 shadow-lg">
                                <User className="h-4 w-4 text-white" />
                            </div>
                        )}
                    </div>
                ))}

                {/* Typing Indicator */}
                {typingIndicator && (
                    <div className="flex gap-3 animate-fade-in">
                        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center flex-shrink-0 shadow-lg">
                            <Bot className="h-4 w-4 text-white" />
                        </div>
                        <div className="flex-1 min-w-0">
                            <div className="bg-white dark:bg-slate-800 rounded-2xl rounded-tl-md p-4 shadow-lg border border-slate-200 dark:border-slate-700 w-fit">
                                <div className="flex items-center gap-2">
                                    <div className="flex space-x-1">
                                        <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                                        <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                        <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                    </div>
                                    <span className="text-xs text-slate-500 dark:text-slate-400 ml-2">AI is typing...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                {/* Message Count */}
                {visibleMessages.length > 0 && (
                    <div className="flex justify-center mt-4">
                        <Badge variant="outline" className="text-xs">
                            {visibleMessages.length} message{visibleMessages.length !== 1 ? 's' : ''} simulated
                        </Badge>
                    </div>
                )}
            </div>
        </div>
    )
} 