"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Code2, FileCode, Globe, Layers } from "lucide-react"

interface EmbedFormatSelectorProps {
    selectedFormat: string
    onFormatChange: (format: string) => void
}

const formats = [
    {
        id: "html",
        name: "HTML/JavaScript",
        description: "Standard script tag integration for any website",
        icon: Globe,
        badge: "Most Popular",
        badgeVariant: "default" as const
    },
    {
        id: "react",
        name: "React Component",
        description: "NPM package for React applications",
        icon: Code2,
        badge: "Developer Friendly",
        badgeVariant: "secondary" as const
    },
    {
        id: "iframe",
        name: "iFrame Embed",
        description: "Isolated iframe for maximum compatibility",
        icon: FileCode,
        badge: "Secure",
        badgeVariant: "outline" as const
    }
]

export function EmbedFormatSelector({ selectedFormat, onFormatChange }: EmbedFormatSelectorProps) {
    return (
        <Card className="shadow-lg border-0 bg-gradient-to-br from-white to-yellow-50 dark:from-slate-900 dark:to-slate-800">
            <CardHeader className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white pb-4 pt-6 rounded-t-lg">
                <CardTitle className="flex items-center gap-2">
                    <Layers className="h-5 w-5" />
                    Integration Method
                </CardTitle>
                <CardDescription className="text-yellow-100">
                    Choose how you want to integrate the chat widget into your website
                </CardDescription>
            </CardHeader>
            <CardContent className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {formats.map((format) => {
                        const Icon = format.icon
                        const isSelected = selectedFormat === format.id

                        return (
                            <Button
                                key={format.id}
                                variant={isSelected ? "default" : "outline"}
                                className={`h-auto p-6 flex flex-col items-start gap-4 text-left transition-all duration-200 ${isSelected
                                        ? "bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 shadow-lg scale-105"
                                        : "hover:bg-muted/50 hover:shadow-md hover:scale-102 border-slate-200 dark:border-slate-700"
                                    }`}
                                onClick={() => onFormatChange(format.id)}
                            >
                                <div className="flex items-center justify-between w-full">
                                    <Icon className={`h-6 w-6 ${isSelected ? "text-white" : "text-slate-600 dark:text-slate-400"}`} />
                                    <Badge
                                        variant={isSelected ? "secondary" : format.badgeVariant}
                                        className={`text-xs ${isSelected
                                                ? "bg-white/20 text-white border-white/30"
                                                : ""
                                            }`}
                                    >
                                        {format.badge}
                                    </Badge>
                                </div>
                                <div className="space-y-2 w-full">
                                    <h3 className={`font-semibold text-base ${isSelected ? "text-white" : "text-slate-800 dark:text-slate-200"
                                        }`}>
                                        {format.name}
                                    </h3>
                                    <p className={`text-sm leading-relaxed ${isSelected
                                            ? "text-white/90"
                                            : "text-muted-foreground"
                                        }`}>
                                        {format.description}
                                    </p>
                                </div>
                            </Button>
                        )
                    })}
                </div>
            </CardContent>
        </Card>
    )
} 