"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Activity, CreditCard, DollarSign, Users, BarChart3, ArrowUpRight, MessageSquare, Zap, Settings, Plus, Bot, ArrowRight, Star, Sparkles, ChevronRight } from "lucide-react"
import { AnalyticsCard } from "@/components/dashboard/analytics-card"
import { WidgetShowcase } from "@/components/dashboard/widget-showcase"
import { WidgetLauncher } from "@/components/widget/widget-launcher"

// Add CSS animations
const globalStyles = `
@keyframes growUp {
  from { height: 0; opacity: 0; }
  to { height: var(--height); opacity: var(--opacity); }
}

@keyframes slideIn {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
`

export default function Dashboard() {
  const [activeTab, setActiveTab] = useState("overview")

  return (
    <div className="space-y-8 p-4 lg:p-6">
      {/* Header with gradient background */}
      <div className="relative overflow-hidden rounded-2xl bg-gradient-to-r from-blue-600 to-indigo-700 p-8 text-white shadow-lg">
        <div className="absolute inset-0 bg-grid-white/10"></div>
        <div className="relative z-10">
          <div className="flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Welcome to ChatWidget Builder</h1>
              <p className="mt-2 text-blue-100">
                Build, customize, and deploy AI chat widgets for your website
              </p>
            </div>
            <Button className="mt-4 sm:mt-0 bg-white text-blue-600 hover:bg-blue-50 shadow-md flex items-center gap-2 group">
              <span>Create New Widget</span>
              <Plus className="h-4 w-4 transition-transform group-hover:rotate-90" />
            </Button>
          </div>

          {/* Quick stats cards */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <AnalyticsCard
              title="Total Widgets"
              value={12}
              icon={<MessageSquare className="h-5 w-5 text-blue-600 dark:text-blue-400" />}
              change={16.7}
              changeText="+2"
              trend="up"
              trendColor="blue"
            />
            <AnalyticsCard
              title="AI Providers"
              value={3}
              icon={<Bot className="h-5 w-5 text-purple-600 dark:text-purple-400" />}
              change={33.3}
              changeText="+1"
              trend="up"
              trendColor="purple"
            />
            <AnalyticsCard
              title="Deployments"
              value={8}
              icon={<Zap className="h-5 w-5 text-green-600 dark:text-green-400" />}
              change={37.5}
              changeText="+3"
              trend="up"
              trendColor="green"
            />
            <AnalyticsCard
              title="Conversations"
              value="2,845"
              icon={<Activity className="h-5 w-5 text-amber-600 dark:text-amber-400" />}
              change={12}
              trend="up"
              trendColor="yellow"
              changeTimeframe="from last month"
            />
          </div>

          {/* Decorative elements */}
          <div className="absolute top-0 right-0 -mt-16 -mr-16 h-64 w-64 rounded-full bg-blue-500/30 blur-3xl"></div>
          <div className="absolute bottom-0 left-0 -mb-16 -ml-16 h-64 w-64 rounded-full bg-indigo-500/30 blur-3xl"></div>
        </div>
      </div>

      {/* Main content area */}
      <div className="space-y-6">
        <Tabs defaultValue="overview" className="w-full" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3 lg:w-auto lg:inline-flex">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              <span>Overview</span>
            </TabsTrigger>
            <TabsTrigger value="widgets" className="flex items-center gap-2">
              <Bot className="h-4 w-4" />
              <span>My Widgets</span>
            </TabsTrigger>
            <TabsTrigger value="providers" className="flex items-center gap-2">
              <Sparkles className="h-4 w-4" />
              <span>AI Providers</span>
            </TabsTrigger>
          </TabsList>

          {/* Overview Tab Content */}
          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
              {/* Main chart */}
              <Card className="col-span-4 h-[400px]">
                <CardHeader className="flex flex-row items-center justify-between">
                  <div>
                    <CardTitle>Analytics Overview</CardTitle>
                    <CardDescription>Your widget performance at a glance</CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm" className="h-8">
                      Weekly
                    </Button>
                    <Button variant="outline" size="sm" className="h-8">
                      Monthly
                    </Button>
                    <Button variant="default" size="sm" className="h-8">
                      Yearly
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  {/* Animated chart placeholder */}
                  <div className="h-[300px] rounded-md bg-slate-50 dark:bg-slate-800/50 flex flex-col items-center justify-end p-4 relative overflow-hidden">
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-indigo-50/50 dark:from-blue-900/10 dark:to-indigo-900/10"></div>

                    {/* Chart grid lines */}
                    <div className="absolute inset-0 flex flex-col justify-between py-8">
                      {[0, 1, 2, 3, 4].map((i) => (
                        <div key={i} className="w-full h-px bg-slate-200 dark:bg-slate-700/50"></div>
                      ))}
                    </div>

                    {/* Chart bars */}
                    <div className="flex items-end justify-between w-full h-full z-10 pt-8 pb-4">
                      {[65, 40, 75, 50, 85, 60, 70, 45, 80, 55, 90, 65].map((height, i) => (
                        <div
                          key={i}
                          className="w-[7%] mx-[0.5%] rounded-t-md bg-gradient-to-t from-blue-500 to-indigo-600 relative group"
                          style={{
                            height: `${height}%`,
                            opacity: 0.7 + (i % 3) * 0.1,
                            animation: `growUp 1s ease-out ${i * 0.1}s both`
                          }}
                        >
                          <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-slate-800 text-white text-xs py-1 px-2 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                            {height * 38}
                          </div>
                        </div>
                      ))}
                    </div>

                    {/* Chart labels */}
                    <div className="flex justify-between w-full mt-2 px-2">
                      {['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'].map((month, i) => (
                        <div key={i} className="text-xs text-slate-500 dark:text-slate-400">
                          {month}
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
                <CardFooter>
                  <h3 className="text-lg font-semibold">Connect AI Provider</h3>
                  <p className="mt-1 text-sm text-slate-500 dark:text-slate-400">
                    Set up or manage your AI provider integrations
                  </p>
                </CardFooter>
              </Card>

              <Card className="p-6 hover:shadow-md transition-all duration-200 group cursor-pointer border-2 border-transparent hover:border-green-100 dark:hover:border-green-900">
                <div className="flex items-start justify-between">
                  <div className="h-12 w-12 rounded-lg bg-green-100 dark:bg-green-900 flex items-center justify-center">
                    <Settings className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <ArrowRight className="h-5 w-5 text-slate-400 group-hover:text-green-500 transition-all duration-200 group-hover:translate-x-1" />
                </div>
                <h3 className="mt-4 text-lg font-semibold">Account Settings</h3>
                <p className="mt-1 text-sm text-slate-500 dark:text-slate-400">
                  Manage your account preferences and billing
                </p>
              </Card>

            </div>
          </TabsContent>

          <TabsContent value="widgets" className="pt-6">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">Your Chat Widgets</h3>
              <div className="space-y-4">
                {/* Widget list would go here */}
                <div className="text-center py-8">
                  <Bot className="h-12 w-12 mx-auto text-slate-300" />
                  <p className="mt-4 text-slate-500">No widgets created yet</p>
                  <Button className="mt-4" size="sm">Create Your First Widget</Button>
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="providers" className="pt-6">
            <Card className="p-6">
              <h3 className="text-lg font-semibold mb-4">AI Provider Connections</h3>
              <div className="space-y-4">
                {/* Provider list would go here */}
                <div className="text-center py-8">
                  <Sparkles className="h-12 w-12 mx-auto text-slate-300" />
                  <p className="mt-4 text-slate-500">No AI providers configured</p>
                  <Button className="mt-4" size="sm">Connect an AI Provider</Button>
                </div>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}