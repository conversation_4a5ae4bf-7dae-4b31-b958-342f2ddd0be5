"use client"

import { useState, useEffect } from "react"
import { Card } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { ArrowUp, ArrowDown, Minus } from "lucide-react"

interface AnalyticsCardProps {
  title: string
  value: number | string
  icon: React.ReactNode
  change?: number
  changeText?: string
  changeTimeframe?: string
  trend?: "up" | "down" | "neutral"
  trendColor?: "green" | "red" | "blue" | "yellow" | "purple" | "default"
  loading?: boolean
  className?: string
  valuePrefix?: string
  valueSuffix?: string
  onClick?: () => void
}

export function AnalyticsCard({
  title,
  value,
  icon,
  change,
  changeText,
  changeTimeframe = "from last week",
  trend = "neutral",
  trendColor = "default",
  loading = false,
  className = "",
  valuePrefix = "",
  valueSuffix = "",
  onClick
}: AnalyticsCardProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  // Animation on mount
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true)
    }, 100)

    return () => clearTimeout(timer)
  }, [])

  // Get trend icon
  const getTrendIcon = () => {
    switch (trend) {
      case "up":
        return <ArrowUp className="h-3 w-3" />
      case "down":
        return <ArrowDown className="h-3 w-3" />
      default:
        return <Minus className="h-3 w-3" />
    }
  }

  // Get trend color
  const getTrendColorClass = () => {
    if (trend === "neutral") return "text-slate-500"

    switch (trendColor) {
      case "green":
        return trend === "up" ? "text-green-600" : "text-red-600"
      case "red":
        return trend === "up" ? "text-red-600" : "text-green-600"
      case "blue":
        return "text-blue-600"
      case "yellow":
        return "text-amber-600"
      case "purple":
        return "text-purple-600"
      default:
        return trend === "up" ? "text-emerald-600" : "text-rose-600"
    }
  }

  return (
    <Card
      className={cn(
        "relative overflow-hidden transition-all duration-300 border border-slate-200 dark:border-slate-700",
        isHovered ? "shadow-lg transform -translate-y-1" : "shadow",
        loading ? "animate-pulse" : "",
        onClick ? "cursor-pointer" : "",
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onClick}
    >
      {/* Background gradient */}
      <div
        className={cn(
          "absolute inset-0 opacity-5 transition-opacity duration-300",
          isHovered ? "opacity-10" : "opacity-5"
        )}
        style={{
          background: `radial-gradient(circle at top right, 
            var(--${trendColor === "default" ?
              (trend === "up" ? "emerald" : trend === "down" ? "rose" : "slate") :
              trendColor}-500), 
            transparent 70%)`
        }}
      />

      <div className="p-6">
        <div className="flex justify-between items-start">
          <div>
            <p className="text-sm font-medium text-slate-500 dark:text-slate-400 mb-1">{title}</p>
            <div className={cn(
              "flex items-baseline transition-transform duration-500 transform",
              isVisible ? "translate-y-0 opacity-100" : "translate-y-4 opacity-0"
            )}>
              <h3 className="text-2xl font-bold tracking-tight">
                {valuePrefix}{loading ? "-" : value}{valueSuffix}
              </h3>

              {(change !== undefined || changeText) && (
                <span className={cn(
                  "ml-2 text-xs font-medium flex items-center gap-0.5",
                  getTrendColorClass()
                )}>
                  {getTrendIcon()}
                  {changeText || `${change}%`}
                </span>
              )}
            </div>

            {changeTimeframe && (
              <p className={cn(
                "text-xs text-slate-500 dark:text-slate-400 mt-1 transition-all duration-500",
                isVisible ? "opacity-100" : "opacity-0"
              )}>
                {changeTimeframe}
              </p>
            )}
          </div>

          <div className={cn(
            "p-2 rounded-full transition-all duration-300",
            isHovered ? "bg-slate-100 dark:bg-slate-800" : "bg-slate-50 dark:bg-slate-900"
          )}>
            {icon}
          </div>
        </div>

        {/* Animated bar chart (simplified visualization) */}
        <div className="mt-4 h-2 flex gap-1">
          {Array.from({ length: 6 }).map((_, i) => {
            // Use deterministic values instead of Math.random() to prevent hydration mismatch
            const widthValues = [23.8, 23.5, 19.7, 22.1, 18.0, 21.7];
            const scaleValues = [0.8, 0.6, 0.9, 0.7, 0.5, 0.8];

            return (
              <div
                key={i}
                className={cn(
                  "h-full rounded-full transition-all duration-500",
                  isVisible ? "opacity-100" : "opacity-0"
                )}
                style={{
                  width: `${widthValues[i]}%`,
                  backgroundColor: `var(--${trendColor === "default" ?
                    (trend === "up" ? "emerald" : trend === "down" ? "rose" : "slate") :
                    trendColor}-${300 + i * 100 > 900 ? 900 : 300 + i * 100})`,
                  transform: `scaleY(${isVisible ? scaleValues[i] : 0})`,
                  opacity: isVisible ? (0.7 - (i * 0.1)) : 0,
                  transitionDelay: `${i * 100}ms`
                }}
              />
            );
          })}
        </div>
      </div>
    </Card>
  )
}
