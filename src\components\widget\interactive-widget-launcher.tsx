"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { motion, AnimatePresence } from "framer-motion"
import { Bot, MessageSquare, X, ChevronRight, Sparkles } from "lucide-react"
import { WidgetConfig } from "@/hooks/use-widget-config"
import { SharedWidgetPreview } from "./shared-widget-preview"

interface InteractiveWidgetLauncherProps {
  config: WidgetConfig
  className?: string
}

export function InteractiveWidgetLauncher({ 
  config, 
  className = "" 
}: InteractiveWidgetLauncherProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [showHint, setShowHint] = useState(false)
  const [hasInteracted, setHasInteracted] = useState(false)
  
  // Show hint after delay if user hasn't interacted
  useEffect(() => {
    if (!hasInteracted) {
      const timer = setTimeout(() => {
        setShowHint(true)
      }, 5000)
      
      return () => clearTimeout(timer)
    }
  }, [hasInteracted])
  
  const handleToggle = () => {
    setIsOpen(prev => !prev)
    setHasInteracted(true)
    setShowHint(false)
  }
  
  const getPositionClasses = () => {
    switch (config.position) {
      case "bottom-right":
        return "bottom-4 right-4"
      case "bottom-left":
        return "bottom-4 left-4"
      case "top-right":
        return "top-4 right-4"
      case "top-left":
        return "top-4 left-4"
      default:
        return "bottom-4 right-4"
    }
  }
  
  return (
    <div className={`fixed ${getPositionClasses()} z-50 ${className}`}>
      <AnimatePresence>
        {isOpen ? (
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="mb-4"
          >
            <SharedWidgetPreview 
              config={config} 
              onClose={() => setIsOpen(false)}
              showCloseButton={true}
            />
          </motion.div>
        ) : (
          <>
            {/* Hint tooltip */}
            <AnimatePresence>
              {showHint && (
                <motion.div
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  className="absolute mb-3 bottom-full right-0 bg-white dark:bg-slate-800 rounded-lg p-3 shadow-lg border border-slate-200 dark:border-slate-700 max-w-[200px] text-sm"
                  style={{
                    marginBottom: "5rem",
                    background: `linear-gradient(to right, ${config.primaryColor}10, ${config.primaryColor}20)`,
                    borderColor: config.primaryColor
                  }}
                >
                  <div className="flex items-start gap-2">
                    <Sparkles className="h-4 w-4 text-blue-500 flex-shrink-0 mt-0.5" />
                    <div>
                      <p className="font-medium text-slate-800 dark:text-slate-200">Need assistance?</p>
                      <p className="text-slate-600 dark:text-slate-300 text-xs mt-1">Click to chat with our AI assistant</p>
                    </div>
                  </div>
                  <div className="absolute -bottom-2 right-5 w-4 h-4 bg-white dark:bg-slate-800 rotate-45 border-r border-b border-slate-200 dark:border-slate-700"
                    style={{
                      background: `${config.primaryColor}20`,
                      borderColor: config.primaryColor
                    }}
                  ></div>
                </motion.div>
              )}
            </AnimatePresence>
            
            {/* Launcher button with pulse effect */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="relative"
            >
              <Button
                onClick={handleToggle}
                size="lg"
                className="h-14 w-14 rounded-full shadow-lg relative overflow-hidden"
                style={{
                  background: `linear-gradient(135deg, ${config.primaryColor}, ${adjustColorBrightness(config.primaryColor, -20)})`
                }}
              >
                <span className="sr-only">Open chat widget</span>
                <MessageSquare className="h-6 w-6 text-white" />
                
                {/* Animated background elements */}
                <div className="absolute inset-0 overflow-hidden">
                  <div className="absolute -top-6 -left-6 w-12 h-12 bg-white/20 rounded-full"></div>
                  <div className="absolute -bottom-8 -right-8 w-16 h-16 bg-white/10 rounded-full"></div>
                </div>
              </Button>
              
              {/* Pulse effect */}
              <span className="absolute inset-0 rounded-full animate-ping-slow bg-blue-400 opacity-30"></span>
              
              {/* Unread indicator */}
              <span className="absolute top-0 right-0 w-4 h-4 bg-red-500 rounded-full border-2 border-white dark:border-slate-900"></span>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  )
}

// Helper function to adjust color brightness
function adjustColorBrightness(color: string, percent: number): string {
  // Simple color brightness adjustment
  const num = parseInt(color.replace("#", ""), 16)
  const amt = Math.round(2.55 * percent)
  const R = (num >> 16) + amt
  const G = (num >> 8 & 0x00FF) + amt
  const B = (num & 0x0000FF) + amt
  return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
      (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
      (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1)
}
