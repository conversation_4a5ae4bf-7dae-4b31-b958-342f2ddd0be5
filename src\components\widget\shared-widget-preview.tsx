"use client"

import { Card } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { X, Send, Minus, Bo<PERSON>, Sparkles } from "lucide-react"
import { WidgetConfig } from "@/hooks/use-widget-config"

interface SharedWidgetPreviewProps {
    config?: WidgetConfig
    isMobile?: boolean
    onClose?: () => void
    showCloseButton?: boolean
    className?: string
}

export function SharedWidgetPreview({
    config,
    isMobile = false,
    onClose,
    showCloseButton = true,
    className = ""
}: SharedWidgetPreviewProps) {
    // Default configuration if none provided
    const defaultConfig: WidgetConfig = {
        // Appearance
        primaryColor: "#3b82f6",
        secondaryColor: "#64748b",
        backgroundColor: "#ffffff",
        textColor: "#1e293b",
        borderColor: "#e2e8f0",
        fontFamily: "Inter",
        fontSize: "14",
        borderRadius: "medium",
        borderWidth: "1",
        shadowSize: "medium",

        // Behavior
        position: "bottom-right",
        widgetSize: "medium",
        marginX: 20,
        marginY: 20,
        autoOpen: false,
        showOnMobile: true,
        allowMinimize: true,
        autoOpenDelay: 3000,
        soundNotifications: true,
        typingIndicator: true,
        messageTimestamps: true,
        responseDelay: 1000,
        zIndex: 1000,
        animationStyle: "slide",

        // Content
        welcomeTitle: "Welcome!",
        welcomeMessage: "How can I help you today?",
        welcomeDelay: 2,
        inputPlaceholder: "Type your message...",
        maxMessageLength: 500,
        chatButtonText: "Chat with us",
        sendButtonText: "Send",
        closeButtonText: "Close Chat",
        minimizeButtonText: "Minimize",
        quickReplies: ["Account Setup", "Pricing Plans", "Contact Support"],
        connectionError: "Unable to connect. Please check your internet connection.",
        typingMessage: "Assistant is typing...",
        offlineMessage: "We're currently offline. Please leave a message.",
        companyName: "ChatWidget",
        companyLogo: "",
        agentName: "AI Assistant",
        agentAvatar: "",
        showPoweredBy: true,
        poweredByText: "Powered by ChatWidget Builder",
        logoUrl: "",
        privacyPolicyUrl: "",
        termsOfServiceUrl: "",
        gdprCompliance: false
    }

    const widgetConfig = config || defaultConfig
    const widgetSizeClasses = getWidgetSizeClasses(widgetConfig.widgetSize, isMobile)

    return (
        <Card className={`${widgetSizeClasses} shadow-2xl border-0 flex flex-col bg-white dark:bg-slate-900 backdrop-blur-xl rounded-2xl ring-1 ring-slate-200 dark:ring-slate-700 ${className}`}>

            {/* Premium Header */}
            <div
                className="p-3 flex items-center justify-between flex-shrink-0 relative z-20 rounded-t-xl"
                style={{
                    background: config ?
                        `linear-gradient(to right, ${config.primaryColor}, ${adjustColorBrightness(config.primaryColor, -20)})` :
                        "linear-gradient(to right, #3b82f6, #6366f1, #8b5cf6)",
                    color: "white"
                }}
            >
                <div className="absolute inset-0 bg-gradient-to-r from-black/10 to-transparent backdrop-blur-sm"></div>
                <div className="flex items-center gap-3 relative z-10">
                    <div className="w-10 h-10 rounded-full bg-white/20 backdrop-blur-sm flex items-center justify-center ring-2 ring-white/30">
                        <Bot className="h-5 w-5 text-white" />
                    </div>
                    <div>
                        <h3 className="font-semibold text-sm">{widgetConfig.agentName}</h3>
                        <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                            <p className="text-xs opacity-90">Online • Powered by GPT-4</p>
                        </div>
                    </div>
                </div>

                {showCloseButton && (
                    <div className="flex items-center gap-2 relative z-10">
                        {widgetConfig.allowMinimize && (
                            <Button variant="ghost" size="icon" className="h-8 w-8 text-white hover:bg-white/20 rounded-lg transition-all">
                                <Minus className="h-4 w-4" />
                            </Button>
                        )}
                        <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-white hover:bg-white/20 rounded-lg transition-all"
                            onClick={onClose}
                        >
                            <X className="h-4 w-4" />
                        </Button>
                    </div>
                )}
                {/* Decorative elements */}
                <div className="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
                <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>
            </div>

            {/* Chat Messages */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gradient-to-b from-slate-50 to-white dark:from-slate-800 dark:to-slate-900">

                {/* AI Welcome Message */}
                <div className="flex gap-3 animate-fade-in">
                    <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center flex-shrink-0 shadow-lg">
                        <Bot className="h-4 w-4 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium mb-2 text-slate-800 dark:text-slate-200">{widgetConfig.agentName}</div>
                        <div className="bg-white dark:bg-slate-800 rounded-2xl rounded-tl-md p-4 shadow-lg border border-slate-200 dark:border-slate-700 max-w-[280px]">
                            <div className="font-semibold text-slate-800 dark:text-slate-200 mb-2 flex items-center gap-2">
                                <Sparkles className="h-4 w-4 text-blue-500" />
                                {widgetConfig.welcomeTitle} 👋
                            </div>
                            <div className="text-slate-600 dark:text-slate-300 text-sm leading-relaxed">
                                {widgetConfig.welcomeMessage}
                            </div>
                        </div>
                        {widgetConfig.messageTimestamps && (
                            <div className="text-xs text-slate-400 mt-2 flex items-center gap-1">
                                <div className="w-1 h-1 bg-green-500 rounded-full"></div>
                                Just now
                            </div>
                        )}
                    </div>
                </div>

                {/* User Message */}
                <div className="flex gap-3 justify-end animate-fade-in" style={{ animationDelay: '0.5s' }}>
                    <div className="flex-1 min-w-0 flex justify-end">
                        <div
                            className="text-white rounded-2xl rounded-tr-md p-4 shadow-lg max-w-[280px]"
                            style={{
                                background: config ?
                                    `linear-gradient(to right, ${config.primaryColor}, ${adjustColorBrightness(config.primaryColor, -10)})` :
                                    "linear-gradient(to right, #3b82f6, #6366f1)"
                            }}
                        >
                            <div className="text-sm leading-relaxed">
                                Hello! I need help with setting up my account and understanding your pricing plans.
                            </div>
                        </div>
                    </div>
                    <div className="w-8 h-8 rounded-full bg-gradient-to-br from-slate-400 to-slate-600 flex items-center justify-center flex-shrink-0 shadow-lg">
                        <span className="text-xs font-semibold text-white">U</span>
                    </div>
                </div>

                {/* AI Response */}
                <div className="flex gap-3 animate-fade-in" style={{ animationDelay: '1s' }}>
                    <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center flex-shrink-0 shadow-lg">
                        <Bot className="h-4 w-4 text-white" />
                    </div>
                    <div className="flex-1 min-w-0">
                        <div className="text-sm font-medium mb-2 text-slate-800 dark:text-slate-200">{widgetConfig.agentName}</div>
                        <div className="bg-white dark:bg-slate-800 rounded-2xl rounded-tl-md p-4 shadow-lg border border-slate-200 dark:border-slate-700 max-w-[280px]">
                            <div className="text-slate-600 dark:text-slate-300 text-sm leading-relaxed">
                                I&apos;d be happy to help you with both! Let me guide you through account setup and explain our pricing options. Which would you like to start with?
                            </div>
                        </div>
                        {widgetConfig.messageTimestamps && (
                            <div className="text-xs text-slate-400 mt-2 flex items-center gap-1">
                                <div className="w-1 h-1 bg-green-500 rounded-full"></div>
                                Just now
                            </div>
                        )}
                    </div>
                </div>

                {/* Quick Replies */}
                {widgetConfig.quickReplies.length > 0 && (
                    <div className="space-y-3 animate-fade-in" style={{ animationDelay: '1.5s' }}>
                        <div className="text-xs text-slate-500 dark:text-slate-400 px-2 font-medium">Quick replies:</div>
                        <div className="flex flex-wrap gap-2">
                            {widgetConfig.quickReplies.slice(0, 3).map((reply, index) => (
                                <Badge
                                    key={index}
                                    variant="outline"
                                    className="cursor-pointer hover:bg-blue-50 hover:border-blue-300 dark:hover:bg-blue-950 text-xs  px-3 rounded-full border-slate-300 dark:border-slate-600 transition-all"
                                    style={{
                                        borderColor: config?.primaryColor || "#3b82f6",
                                        color: config?.primaryColor || "#3b82f6"
                                    }}
                                >
                                    {reply}
                                </Badge>
                            ))}
                        </div>
                    </div>
                )}

                {/* Typing Indicator */}
                {widgetConfig.typingIndicator && (
                    <div className="flex gap-3 animate-fade-in" style={{ animationDelay: '2s' }}>
                        <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center flex-shrink-0 shadow-lg">
                            <Bot className="h-4 w-4 text-white" />
                        </div>
                        <div className="flex-1 min-w-0">
                            <div className="bg-white dark:bg-slate-800 rounded-2xl rounded-tl-md p-4 shadow-lg border border-slate-200 dark:border-slate-700 w-fit">
                                <div className="flex items-center gap-2">
                                    <div className="flex space-x-1">
                                        <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce"></div>
                                        <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                        <div className="w-2 h-2 bg-slate-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                    </div>
                                    <span className="text-xs text-slate-500 dark:text-slate-400 ml-2">AI is typing...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </div>

            {/* Enhanced Input Area */}
            <div className="p-4 border-t border-slate-200 dark:border-slate-700 bg-gradient-to-r from-slate-50 to-white dark:from-slate-800 dark:to-slate-900 flex-shrink-0">
                <div className="flex gap-3 items-end">
                    <div className="flex-1 relative">
                        <Input
                            placeholder={widgetConfig.inputPlaceholder}
                            className="h-11 text-sm border-2 border-slate-200 dark:border-slate-600 rounded-2xl bg-white dark:bg-slate-800 focus:ring-2 focus:border-transparent transition-all duration-200 shadow-sm hover:shadow-md pr-12 placeholder:text-slate-400 dark:placeholder:text-slate-500"
                            style={{
                                borderColor: config?.borderColor || "#e2e8f0",
                                borderRadius: getBorderRadius(widgetConfig.borderRadius),
                                fontSize: getFontSize(widgetConfig.fontSize),
                                fontFamily: getFontFamily(widgetConfig.fontFamily),
                                '--tw-ring-color': config?.primaryColor || "#3b82f6"
                            } as React.CSSProperties}
                        />
                        {/* Character counter for long messages */}
                        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-slate-400 dark:text-slate-500 pointer-events-none">
                            0/{widgetConfig.maxMessageLength}
                        </div>
                    </div>
                    <Button
                        size="icon"
                        className="shrink-0 w-11 h-11 rounded-2xl shadow-lg transition-all duration-200 hover:scale-105 hover:shadow-xl border-0 relative overflow-hidden group"
                        style={{
                            background: config ?
                                `linear-gradient(135deg, ${config.primaryColor}, ${adjustColorBrightness(config.primaryColor, -15)})` :
                                "linear-gradient(135deg, #3b82f6, #1e40af)",
                            borderRadius: getBorderRadius(widgetConfig.borderRadius)
                        }}
                    >
                        {/* Animated background on hover */}
                        <div className="absolute inset-0 bg-white/20 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
                        <Send className="h-4 w-4 relative z-10 transition-transform duration-200 group-hover:translate-x-0.5" />
                    </Button>
                </div>

                {/* Input hints/features */}
                <div className="flex items-center justify-between mt-2 px-1">
                    <div className="flex items-center gap-2 text-xs text-slate-400 dark:text-slate-500">
                        <div className="flex items-center gap-1">
                            <div className="w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"></div>
                            <span>Online</span>
                        </div>
                        {widgetConfig.responseDelay > 0 && (
                            <div className="flex items-center gap-1">
                                <div className="w-1 h-1 bg-slate-300 rounded-full"></div>
                                <span>Avg. response: {widgetConfig.responseDelay}s</span>
                            </div>
                        )}
                    </div>
                    <div className="text-xs text-slate-400 dark:text-slate-500">
                        Press Enter to send
                    </div>
                </div>
            </div>

            {/* Brand Footer - Sticks to bottom */}
            {widgetConfig.showPoweredBy && (
                <div className="bg-slate-50 dark:bg-slate-800 border-t border-slate-200 dark:border-slate-700 py-1.5 flex items-center justify-center flex-shrink-0">
                    <div className="flex items-center gap-1.5 px-2 py-1 bg-white dark:bg-slate-900 rounded-full border border-slate-200 dark:border-slate-700 shadow-sm">
                        <div className="w-4 h-4 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center">
                            <Sparkles className="h-2.5 w-2.5 text-white" />
                        </div>
                        <span className="text-xs font-medium text-slate-600 dark:text-slate-300">{widgetConfig.companyName}</span>
                    </div>
                </div>
            )}
        </Card>
    )
}

// Helper functions
function getWidgetSizeClasses(size: string, isMobile: boolean): string {
    if (isMobile) {
        switch (size) {
            case "small":
                return "w-64 h-[320px]"
            case "medium":
                return "w-72 h-[380px]"
            case "large":
                return "w-72 h-[420px]"
            case "fullscreen":
                return "w-full h-full"
            default:
                return "w-72 h-[380px]"
        }
    }

    switch (size) {
        case "small":
            return "w-80 h-[380px]"
        case "medium":
            return "w-96 h-[440px]"
        case "large":
            return "w-[28rem] h-[520px]"
        case "fullscreen":
            return "w-full h-full"
        default:
            return "w-96 h-[440px]"
    }
}

function getBorderRadius(radius: string): string {
    switch (radius) {
        case "none":
            return "0px"
        case "small":
            return "4px"
        case "medium":
            return "8px"
        case "large":
            return "12px"
        case "xl":
            return "16px"
        default:
            return "8px"
    }
}

function getFontSize(size: string): string {
    switch (size) {
        case "small":
            return "12px"
        case "medium":
            return "14px"
        case "large":
            return "16px"
        default:
            return "14px"
    }
}

function getFontFamily(family: string): string {
    switch (family) {
        case "inter":
            return "Inter, system-ui, sans-serif"
        case "roboto":
            return "Roboto, system-ui, sans-serif"
        case "open-sans":
            return "Open Sans, system-ui, sans-serif"
        case "lato":
            return "Lato, system-ui, sans-serif"
        case "poppins":
            return "Poppins, system-ui, sans-serif"
        case "montserrat":
            return "Montserrat, system-ui, sans-serif"
        default:
            return "Inter, system-ui, sans-serif"
    }
}

function adjustColorBrightness(color: string, percent: number): string {
    // Simple color brightness adjustment
    const num = parseInt(color.replace("#", ""), 16)
    const amt = Math.round(2.55 * percent)
    const R = (num >> 16) + amt
    const G = (num >> 8 & 0x00FF) + amt
    const B = (num & 0x0000FF) + amt
    return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
        (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
        (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1)
} 