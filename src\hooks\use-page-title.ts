"use client"

import { usePathname } from "next/navigation"

const pageTitles: Record<string, string> = {
    "/": "Dashboard",
    "/widget-builder": "Widget Builder",
    "/providers": "AI Providers",
    "/embed-code": "Embed Code",
    "/testing-lab": "Testing Lab",
    "/settings": "Settings",
}

export function usePageTitle() {
    const pathname = usePathname()
    return pageTitles[pathname] || "Dashboard"
} 