"use client"

import { useState, useCallback } from "react"

export interface WidgetConfig {
    // Appearance
    primaryColor: string
    secondaryColor: string
    backgroundColor: string
    textColor: string
    borderColor: string
    fontFamily: string
    fontSize: string
    borderRadius: string
    borderWidth: string
    shadowSize: string

    // Behavior
    position: string
    widgetSize: string
    marginX: number
    marginY: number
    autoOpen: boolean
    showOnMobile: boolean
    allowMinimize: boolean
    autoOpenDelay: number
    soundNotifications: boolean
    typingIndicator: boolean
    messageTimestamps: boolean
    responseDelay: number
    zIndex: number
    animationStyle: string

    // Content
    welcomeTitle: string
    welcomeMessage: string
    welcomeDelay: number
    inputPlaceholder: string
    maxMessageLength: number
    chatButtonText: string
    sendButtonText: string
    closeButtonText: string
    minimizeButtonText: string
    quickReplies: string[]
    connectionError: string
    typingMessage: string
    offlineMessage: string
    companyName: string
    companyLogo: string
    agentName: string
    agentAvatar: string
    showPoweredBy: boolean
    poweredByText: string
    logoUrl: string
    privacyPolicyUrl: string
    termsOfServiceUrl: string
    gdprCompliance: boolean
}

const defaultConfig: WidgetConfig = {
    // Appearance defaults
    primaryColor: "#3b82f6",
    secondaryColor: "#64748b",
    backgroundColor: "#ffffff",
    textColor: "#1f2937",
    borderColor: "#e5e7eb",
    fontFamily: "inter",
    fontSize: "medium",
    borderRadius: "medium",
    borderWidth: "1",
    shadowSize: "medium",

    // Behavior defaults
    position: "bottom-right",
    widgetSize: "medium",
    marginX: 20,
    marginY: 20,
    autoOpen: false,
    showOnMobile: true,
    allowMinimize: true,
    autoOpenDelay: 3,
    soundNotifications: false,
    typingIndicator: true,
    messageTimestamps: true,
    responseDelay: 1,
    zIndex: 9999,
    animationStyle: "slide",

    // Content defaults
    welcomeTitle: "Hi there! 👋",
    welcomeMessage: "How can I help you today?",
    welcomeDelay: 2,
    inputPlaceholder: "Type your message here...",
    maxMessageLength: 500,
    chatButtonText: "Start Chat",
    sendButtonText: "Send",
    closeButtonText: "Close Chat",
    minimizeButtonText: "Minimize",
    quickReplies: [
        "What are your business hours?",
        "How do I get started?",
        "Contact support"
    ],
    connectionError: "Unable to connect. Please check your internet connection.",
    typingMessage: "Assistant is typing...",
    offlineMessage: "We're currently offline. Please leave a message and we'll get back to you soon.",
    companyName: "Acme Corp",
    companyLogo: "",
    agentName: "AI Assistant",
    agentAvatar: "",
    showPoweredBy: true,
    poweredByText: "Powered by ChatWidget Builder",
    logoUrl: "",
    privacyPolicyUrl: "",
    termsOfServiceUrl: "",
    gdprCompliance: false
}

export function useWidgetConfig() {
    const [config, setConfig] = useState<WidgetConfig>(defaultConfig)

    const updateConfig = useCallback((updates: Partial<WidgetConfig>) => {
        setConfig(prev => ({ ...prev, ...updates }))
    }, [])

    const resetConfig = useCallback(() => {
        setConfig(defaultConfig)
    }, [])

    const updateQuickReply = useCallback((index: number, value: string) => {
        setConfig(prev => ({
            ...prev,
            quickReplies: prev.quickReplies.map((reply, i) => i === index ? value : reply)
        }))
    }, [])

    const addQuickReply = useCallback((value: string) => {
        setConfig(prev => ({
            ...prev,
            quickReplies: [...prev.quickReplies, value]
        }))
    }, [])

    const removeQuickReply = useCallback((index: number) => {
        setConfig(prev => ({
            ...prev,
            quickReplies: prev.quickReplies.filter((_, i) => i !== index)
        }))
    }, [])

    return {
        config,
        updateConfig,
        resetConfig,
        updateQuickReply,
        addQuickReply,
        removeQuickReply
    }
} 