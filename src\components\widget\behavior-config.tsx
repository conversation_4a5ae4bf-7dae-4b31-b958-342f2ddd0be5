"use client"

import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { WidgetConfig } from "@/hooks/use-widget-config"

interface BehaviorConfigProps {
    config: WidgetConfig
    updateConfig: (updates: Partial<WidgetConfig>) => void
}

export function BehaviorConfig({ config, updateConfig }: BehaviorConfigProps) {
    return (
        <div className="space-y-6">
            <Accordion
                type="multiple"
                defaultValue={["position", "triggers", "interactions", "advanced"]}
                className="space-y-4"
            >
                {/* Widget Position Section */}
                <AccordionItem
                    value="position"
                    className="bg-white dark:bg-slate-900 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden"
                >
                    <AccordionTrigger className="px-6 py-4 text-lg font-semibold hover:no-underline hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                        Widget Position
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-6">
                            <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                Configure where the widget appears on your website and its dimensions
                            </p>

                            {/* Position and Size */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-3">
                                    <Label htmlFor="position" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Position
                                    </Label>
                                    <Select value={config.position} onValueChange={(value) => updateConfig({ position: value })}>
                                        <SelectTrigger className="h-10">
                                            <SelectValue placeholder="Select position" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="bottom-right">Bottom Right</SelectItem>
                                            <SelectItem value="bottom-left">Bottom Left</SelectItem>
                                            <SelectItem value="top-right">Top Right</SelectItem>
                                            <SelectItem value="top-left">Top Left</SelectItem>
                                            <SelectItem value="center">Center</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-3">
                                    <Label htmlFor="widget-size" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Widget Size
                                    </Label>
                                    <Select value={config.widgetSize} onValueChange={(value) => updateConfig({ widgetSize: value })}>
                                        <SelectTrigger className="h-10">
                                            <SelectValue placeholder="Select size" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="small">Small (320x400)</SelectItem>
                                            <SelectItem value="medium">Medium (380x500)</SelectItem>
                                            <SelectItem value="large">Large (450x600)</SelectItem>
                                            <SelectItem value="fullscreen">Fullscreen</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>

                            {/* Margins */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-3">
                                    <Label htmlFor="margin-x" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Horizontal Margin
                                    </Label>
                                    <div className="flex items-center gap-3">
                                        <Input
                                            id="margin-x"
                                            type="number"
                                            value={config.marginX}
                                            onChange={(e) => updateConfig({ marginX: parseInt(e.target.value) || 0 })}
                                            min="0"
                                            max="100"
                                            className="flex-1 h-10"
                                        />
                                        <span className="text-sm text-slate-500 dark:text-slate-400 font-medium">px</span>
                                    </div>
                                </div>

                                <div className="space-y-3">
                                    <Label htmlFor="margin-y" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Vertical Margin
                                    </Label>
                                    <div className="flex items-center gap-3">
                                        <Input
                                            id="margin-y"
                                            type="number"
                                            value={config.marginY}
                                            onChange={(e) => updateConfig({ marginY: parseInt(e.target.value) || 0 })}
                                            min="0"
                                            max="100"
                                            className="flex-1 h-10"
                                        />
                                        <span className="text-sm text-slate-500 dark:text-slate-400 font-medium">px</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Trigger Settings Section */}
                <AccordionItem
                    value="triggers"
                    className="bg-white dark:bg-slate-900 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden"
                >
                    <AccordionTrigger className="px-6 py-4 text-lg font-semibold hover:no-underline hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                        Trigger Settings
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-6">
                            <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                Configure when and how the widget appears to your visitors
                            </p>

                            {/* Toggle Settings */}
                            <div className="space-y-6">
                                <div className="flex items-start justify-between gap-4 p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                                    <div className="space-y-1 flex-1">
                                        <Label htmlFor="auto-open" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                            Auto Open
                                        </Label>
                                        <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                            Automatically open the widget when the page loads
                                        </p>
                                    </div>
                                    <Switch
                                        id="auto-open"
                                        checked={config.autoOpen}
                                        onCheckedChange={(checked) => updateConfig({ autoOpen: checked })}
                                    />
                                </div>

                                <div className="flex items-start justify-between gap-4 p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                                    <div className="space-y-1 flex-1">
                                        <Label htmlFor="show-on-mobile" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                            Show on Mobile
                                        </Label>
                                        <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                            Display the widget on mobile devices and tablets
                                        </p>
                                    </div>
                                    <Switch
                                        id="show-on-mobile"
                                        checked={config.showOnMobile}
                                        onCheckedChange={(checked) => updateConfig({ showOnMobile: checked })}
                                    />
                                </div>

                                <div className="flex items-start justify-between gap-4 p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                                    <div className="space-y-1 flex-1">
                                        <Label htmlFor="minimize-option" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                            Allow Minimize
                                        </Label>
                                        <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                            Allow users to minimize the widget to save screen space
                                        </p>
                                    </div>
                                    <Switch
                                        id="minimize-option"
                                        checked={config.allowMinimize}
                                        onCheckedChange={(checked) => updateConfig({ allowMinimize: checked })}
                                    />
                                </div>
                            </div>

                            {/* Auto Open Delay */}
                            <div className="space-y-3">
                                <Label htmlFor="delay" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                    Auto Open Delay
                                </Label>
                                <div className="flex items-center gap-3">
                                    <Input
                                        id="delay"
                                        type="number"
                                        value={config.autoOpenDelay}
                                        onChange={(e) => updateConfig({ autoOpenDelay: parseInt(e.target.value) || 0 })}
                                        min="0"
                                        max="60"
                                        className="flex-1 h-10"
                                    />
                                    <span className="text-sm text-slate-500 dark:text-slate-400 font-medium">seconds</span>
                                </div>
                                <p className="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">
                                    Delay before automatically opening the widget (0 = immediate)
                                </p>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Interaction Settings Section */}
                <AccordionItem
                    value="interactions"
                    className="bg-white dark:bg-slate-900 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden"
                >
                    <AccordionTrigger className="px-6 py-4 text-lg font-semibold hover:no-underline hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                        Interaction Settings
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-6">
                            <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                Configure user interaction behaviors and feedback mechanisms
                            </p>

                            {/* Interaction Toggles */}
                            <div className="space-y-6">
                                <div className="flex items-start justify-between gap-4 p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                                    <div className="space-y-1 flex-1">
                                        <Label htmlFor="sound-notifications" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                            Sound Notifications
                                        </Label>
                                        <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                            Play notification sounds for new messages and interactions
                                        </p>
                                    </div>
                                    <Switch
                                        id="sound-notifications"
                                        checked={config.soundNotifications}
                                        onCheckedChange={(checked) => updateConfig({ soundNotifications: checked })}
                                    />
                                </div>

                                <div className="flex items-start justify-between gap-4 p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                                    <div className="space-y-1 flex-1">
                                        <Label htmlFor="typing-indicator" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                            Typing Indicator
                                        </Label>
                                        <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                            Show animated typing indicator when AI is responding
                                        </p>
                                    </div>
                                    <Switch
                                        id="typing-indicator"
                                        checked={config.showTypingIndicator}
                                        onCheckedChange={(checked) => updateConfig({ showTypingIndicator: checked })}
                                    />
                                </div>

                                <div className="flex items-start justify-between gap-4 p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
                                    <div className="space-y-1 flex-1">
                                        <Label htmlFor="timestamps" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                            Message Timestamps
                                        </Label>
                                        <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                            Display timestamps for each message in the conversation
                                        </p>
                                    </div>
                                    <Switch
                                        id="timestamps"
                                        checked={config.showTimestamps}
                                        onCheckedChange={(checked) => updateConfig({ showTimestamps: checked })}
                                    />
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Advanced Settings Section */}
                <AccordionItem
                    value="advanced"
                    className="bg-white dark:bg-slate-900 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden"
                >
                    <AccordionTrigger className="px-6 py-4 text-lg font-semibold hover:no-underline hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                        Advanced Settings
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-6">
                            <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                Advanced configuration options for developers and power users
                            </p>

                            {/* Z-Index and Animation */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-3">
                                    <Label htmlFor="z-index" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Z-Index
                                    </Label>
                                    <Input
                                        id="z-index"
                                        type="number"
                                        value={config.zIndex}
                                        onChange={(e) => updateConfig({ zIndex: parseInt(e.target.value) || 1000 })}
                                        min="1"
                                        max="9999"
                                        className="h-10"
                                    />
                                    <p className="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">
                                        Controls widget layering (higher values appear on top)
                                    </p>
                                </div>

                                <div className="space-y-3">
                                    <Label htmlFor="animation-style" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Animation Style
                                    </Label>
                                    <Select value={config.animationStyle} onValueChange={(value) => updateConfig({ animationStyle: value })}>
                                        <SelectTrigger className="h-10">
                                            <SelectValue placeholder="Select animation" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="fade">Fade In/Out</SelectItem>
                                            <SelectItem value="slide">Slide In/Out</SelectItem>
                                            <SelectItem value="scale">Scale In/Out</SelectItem>
                                            <SelectItem value="bounce">Bounce In/Out</SelectItem>
                                            <SelectItem value="none">No Animation</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>

                            {/* Custom CSS */}
                            <div className="space-y-3">
                                <Label htmlFor="custom-css" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                    Custom CSS Classes
                                </Label>
                                <Input
                                    id="custom-css"
                                    value={config.customCssClasses}
                                    onChange={(e) => updateConfig({ customCssClasses: e.target.value })}
                                    placeholder="custom-widget-class another-class"
                                    className="h-10 font-mono text-sm"
                                />
                                <p className="text-xs text-slate-500 dark:text-slate-400 leading-relaxed">
                                    Add custom CSS classes for advanced styling (space-separated)
                                </p>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>
            </Accordion>
        </div>
    )
} 