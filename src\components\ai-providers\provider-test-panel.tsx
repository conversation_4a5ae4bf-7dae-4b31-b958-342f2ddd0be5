"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Play, RotateCcw, Clock, CheckCircle, XCircle, Activity } from "lucide-react"

interface Provider {
    id: string
    name: string
    description: string
    logo: string
    status: "connected" | "disconnected"
    models: string[]
    pricing: string
    features: string[]
    isPopular: boolean
}

interface ProviderTestPanelProps {
    providers: Provider[]
}

export function ProviderTestPanel({ providers }: ProviderTestPanelProps) {
    const [selectedProvider, setSelectedProvider] = useState(providers[0]?.id || "")
    const [selectedModel, setSelectedModel] = useState("")
    const [testMessage, setTestMessage] = useState("Hello! Can you help me with customer support?")
    const [isLoading, setIsLoading] = useState(false)
    const [response, setResponse] = useState("")
    const [responseTime, setResponseTime] = useState<number | null>(null)

    const selectedProviderData = providers.find(p => p.id === selectedProvider)

    const handleTest = async () => {
        setIsLoading(true)
        setResponse("")
        setResponseTime(null)

        const startTime = Date.now()

        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 2000))

        const endTime = Date.now()
        setResponseTime(endTime - startTime)
        setResponse("Hello! I'd be happy to help you with customer support. What specific issue or question do you have? I can assist with account questions, technical problems, billing inquiries, or general information about our services.")
        setIsLoading(false)
    }

    const testHistory = [
        {
            id: 1,
            provider: "OpenAI",
            model: "gpt-4",
            timestamp: "2024-01-15 14:30:22",
            responseTime: 1250,
            status: "success",
            tokens: 45
        },
        {
            id: 2,
            provider: "OpenAI",
            model: "gpt-3.5-turbo",
            timestamp: "2024-01-15 14:28:15",
            responseTime: 890,
            status: "success",
            tokens: 38
        },
        {
            id: 3,
            provider: "Anthropic",
            model: "claude-3-sonnet",
            timestamp: "2024-01-15 14:25:10",
            responseTime: 2100,
            status: "error",
            tokens: 0
        }
    ]

    return (
        <div className="space-y-6">
            <div>
                <h2 className="text-2xl font-bold mb-2">Test & Monitor</h2>
                <p className="text-muted-foreground">
                    Test your AI provider integrations and monitor their performance
                </p>
            </div>

            <Tabs defaultValue="test" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="test">Live Testing</TabsTrigger>
                    <TabsTrigger value="monitor">Performance Monitor</TabsTrigger>
                </TabsList>

                <TabsContent value="test" className="space-y-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Test Configuration */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Test Configuration</CardTitle>
                                <CardDescription>
                                    Configure and run tests against your AI providers
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="provider">Provider</Label>
                                    <Select value={selectedProvider} onValueChange={setSelectedProvider}>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select provider" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {providers.map((provider) => (
                                                <SelectItem key={provider.id} value={provider.id}>
                                                    <div className="flex items-center gap-2">
                                                        <span>{provider.logo}</span>
                                                        {provider.name}
                                                    </div>
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>

                                {selectedProviderData && (
                                    <div className="space-y-2">
                                        <Label htmlFor="model">Model</Label>
                                        <Select value={selectedModel} onValueChange={setSelectedModel}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select model" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {selectedProviderData.models.map((model) => (
                                                    <SelectItem key={model} value={model}>
                                                        {model}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                )}

                                <div className="space-y-2">
                                    <Label htmlFor="message">Test Message</Label>
                                    <Textarea
                                        id="message"
                                        value={testMessage}
                                        onChange={(e) => setTestMessage(e.target.value)}
                                        placeholder="Enter your test message..."
                                        rows={4}
                                    />
                                </div>

                                <Button
                                    onClick={handleTest}
                                    disabled={!selectedProvider || !selectedModel || !testMessage || isLoading}
                                    className="w-full"
                                >
                                    {isLoading ? (
                                        <>
                                            <RotateCcw className="h-4 w-4 mr-2 animate-spin" />
                                            Testing...
                                        </>
                                    ) : (
                                        <>
                                            <Play className="h-4 w-4 mr-2" />
                                            Run Test
                                        </>
                                    )}
                                </Button>
                            </CardContent>
                        </Card>

                        {/* Test Results */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Test Results</CardTitle>
                                <CardDescription>
                                    View the response from your AI provider
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {responseTime && (
                                    <div className="flex items-center gap-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                        <CheckCircle className="h-5 w-5 text-green-600" />
                                        <div className="flex-1">
                                            <div className="font-medium">Test Successful</div>
                                            <div className="text-sm text-muted-foreground">
                                                Response time: {responseTime}ms
                                            </div>
                                        </div>
                                    </div>
                                )}

                                <div className="space-y-2">
                                    <Label>Response</Label>
                                    <div className="min-h-[200px] p-3 border rounded-lg bg-muted/50">
                                        {isLoading ? (
                                            <div className="flex items-center justify-center h-full">
                                                <RotateCcw className="h-6 w-6 animate-spin text-muted-foreground" />
                                            </div>
                                        ) : response ? (
                                            <p className="text-sm">{response}</p>
                                        ) : (
                                            <p className="text-sm text-muted-foreground">
                                                Run a test to see the AI response here
                                            </p>
                                        )}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </TabsContent>

                <TabsContent value="monitor" className="space-y-6">
                    {/* Performance Metrics */}
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Avg Response Time</CardTitle>
                                <Clock className="h-4 w-4 text-muted-foreground" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">1.2s</div>
                                <p className="text-xs text-muted-foreground">
                                    +0.1s from last hour
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                                <CheckCircle className="h-4 w-4 text-green-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">98.5%</div>
                                <p className="text-xs text-muted-foreground">
                                    +0.2% from yesterday
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Total Requests</CardTitle>
                                <Activity className="h-4 w-4 text-blue-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">2,847</div>
                                <p className="text-xs text-muted-foreground">
                                    +12% from last week
                                </p>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                <CardTitle className="text-sm font-medium">Error Rate</CardTitle>
                                <XCircle className="h-4 w-4 text-red-600" />
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">1.5%</div>
                                <p className="text-xs text-muted-foreground">
                                    -0.3% from yesterday
                                </p>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Test History */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Test History</CardTitle>
                            <CardDescription>
                                View your recent API tests and their results
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="space-y-4">
                                {testHistory.map((test) => (
                                    <div key={test.id} className="flex items-center justify-between p-3 border rounded-lg">
                                        <div className="flex items-center gap-3">
                                            <div className={`w-2 h-2 rounded-full ${test.status === "success" ? "bg-green-500" : "bg-red-500"
                                                }`} />
                                            <div>
                                                <div className="font-medium">{test.provider} - {test.model}</div>
                                                <div className="text-sm text-muted-foreground">{test.timestamp}</div>
                                            </div>
                                        </div>
                                        <div className="flex items-center gap-4 text-sm">
                                            <div className="text-center">
                                                <div className="font-medium">{test.responseTime}ms</div>
                                                <div className="text-muted-foreground">Response</div>
                                            </div>
                                            <div className="text-center">
                                                <div className="font-medium">{test.tokens}</div>
                                                <div className="text-muted-foreground">Tokens</div>
                                            </div>
                                            <Badge variant={test.status === "success" ? "secondary" : "destructive"}>
                                                {test.status}
                                            </Badge>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    )
} 