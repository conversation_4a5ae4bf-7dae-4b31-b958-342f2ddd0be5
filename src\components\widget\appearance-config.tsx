"use client"

import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { WidgetConfig } from "@/hooks/use-widget-config"

interface AppearanceConfigProps {
    config: WidgetConfig
    updateConfig: (updates: Partial<WidgetConfig>) => void
}

export function AppearanceConfig({ config, updateConfig }: AppearanceConfigProps) {
    return (
        <div className="space-y-6">
            <Accordion
                type="multiple"
                defaultValue={["colors", "typography", "borders", "shadows"]}
                className="space-y-4"
            >
                {/* Theme Colors Section */}
                <AccordionItem
                    value="colors"
                    className="bg-white dark:bg-slate-900 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden"
                >
                    <AccordionTrigger className="px-6 py-4 text-lg font-semibold hover:no-underline hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                        Theme Colors
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-6">
                            <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                Customize the color scheme of your widget to match your brand
                            </p>

                            {/* Primary and Secondary Colors */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-3">
                                    <Label htmlFor="primary-color" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Primary Color
                                    </Label>
                                    <div className="flex gap-3">
                                        <Input
                                            id="primary-color"
                                            type="color"
                                            value={config.primaryColor}
                                            onChange={(e) => updateConfig({ primaryColor: e.target.value })}
                                            className="w-12 h-10 p-1 border-2 rounded-lg cursor-pointer"
                                        />
                                        <Input
                                            value={config.primaryColor}
                                            onChange={(e) => updateConfig({ primaryColor: e.target.value })}
                                            placeholder="#3b82f6"
                                            className="flex-1 font-mono text-sm"
                                        />
                                    </div>
                                </div>

                                <div className="space-y-3">
                                    <Label htmlFor="secondary-color" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Secondary Color
                                    </Label>
                                    <div className="flex gap-3">
                                        <Input
                                            id="secondary-color"
                                            type="color"
                                            value={config.secondaryColor}
                                            onChange={(e) => updateConfig({ secondaryColor: e.target.value })}
                                            className="w-12 h-10 p-1 border-2 rounded-lg cursor-pointer"
                                        />
                                        <Input
                                            value={config.secondaryColor}
                                            onChange={(e) => updateConfig({ secondaryColor: e.target.value })}
                                            placeholder="#64748b"
                                            className="flex-1 font-mono text-sm"
                                        />
                                    </div>
                                </div>
                            </div>

                            {/* Background and Text Colors */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-3">
                                    <Label htmlFor="background-color" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Background Color
                                    </Label>
                                    <div className="flex gap-3">
                                        <Input
                                            id="background-color"
                                            type="color"
                                            value={config.backgroundColor}
                                            onChange={(e) => updateConfig({ backgroundColor: e.target.value })}
                                            className="w-12 h-10 p-1 border-2 rounded-lg cursor-pointer"
                                        />
                                        <Input
                                            value={config.backgroundColor}
                                            onChange={(e) => updateConfig({ backgroundColor: e.target.value })}
                                            placeholder="#ffffff"
                                            className="flex-1 font-mono text-sm"
                                        />
                                    </div>
                                </div>

                                <div className="space-y-3">
                                    <Label htmlFor="text-color" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Text Color
                                    </Label>
                                    <div className="flex gap-3">
                                        <Input
                                            id="text-color"
                                            type="color"
                                            value={config.textColor}
                                            onChange={(e) => updateConfig({ textColor: e.target.value })}
                                            className="w-12 h-10 p-1 border-2 rounded-lg cursor-pointer"
                                        />
                                        <Input
                                            value={config.textColor}
                                            onChange={(e) => updateConfig({ textColor: e.target.value })}
                                            placeholder="#1f2937"
                                            className="flex-1 font-mono text-sm"
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Typography Section */}
                <AccordionItem
                    value="typography"
                    className="bg-white dark:bg-slate-900 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden"
                >
                    <AccordionTrigger className="px-6 py-4 text-lg font-semibold hover:no-underline hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                        Typography
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-6">
                            <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                Configure font family and text styling for optimal readability
                            </p>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-3">
                                    <Label htmlFor="font-family" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Font Family
                                    </Label>
                                    <Select value={config.fontFamily} onValueChange={(value) => updateConfig({ fontFamily: value })}>
                                        <SelectTrigger className="h-10">
                                            <SelectValue placeholder="Select font family" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="inter">Inter</SelectItem>
                                            <SelectItem value="roboto">Roboto</SelectItem>
                                            <SelectItem value="open-sans">Open Sans</SelectItem>
                                            <SelectItem value="lato">Lato</SelectItem>
                                            <SelectItem value="poppins">Poppins</SelectItem>
                                            <SelectItem value="system">System Default</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-3">
                                    <Label htmlFor="font-size" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Font Size
                                    </Label>
                                    <Select value={config.fontSize} onValueChange={(value) => updateConfig({ fontSize: value })}>
                                        <SelectTrigger className="h-10">
                                            <SelectValue placeholder="Select font size" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="small">Small (14px)</SelectItem>
                                            <SelectItem value="medium">Medium (16px)</SelectItem>
                                            <SelectItem value="large">Large (18px)</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Border & Shape Section */}
                <AccordionItem
                    value="borders"
                    className="bg-white dark:bg-slate-900 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden"
                >
                    <AccordionTrigger className="px-6 py-4 text-lg font-semibold hover:no-underline hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                        Border &amp; Shape
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-6">
                            <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                Customize the widget's border styling and corner radius
                            </p>

                            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div className="space-y-3">
                                    <Label htmlFor="border-radius" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Border Radius
                                    </Label>
                                    <Select value={config.borderRadius} onValueChange={(value) => updateConfig({ borderRadius: value })}>
                                        <SelectTrigger className="h-10">
                                            <SelectValue placeholder="Select border radius" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="none">None (0px)</SelectItem>
                                            <SelectItem value="small">Small (4px)</SelectItem>
                                            <SelectItem value="medium">Medium (8px)</SelectItem>
                                            <SelectItem value="large">Large (12px)</SelectItem>
                                            <SelectItem value="xl">Extra Large (16px)</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>

                                <div className="space-y-3">
                                    <Label htmlFor="border-width" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                        Border Width
                                    </Label>
                                    <Select value={config.borderWidth} onValueChange={(value) => updateConfig({ borderWidth: value })}>
                                        <SelectTrigger className="h-10">
                                            <SelectValue placeholder="Select border width" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="0">None</SelectItem>
                                            <SelectItem value="1">1px</SelectItem>
                                            <SelectItem value="2">2px</SelectItem>
                                            <SelectItem value="3">3px</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                            </div>

                            <div className="space-y-3">
                                <Label htmlFor="border-color" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                    Border Color
                                </Label>
                                <div className="flex gap-3">
                                    <Input
                                        id="border-color"
                                        type="color"
                                        value={config.borderColor}
                                        onChange={(e) => updateConfig({ borderColor: e.target.value })}
                                        className="w-12 h-10 p-1 border-2 rounded-lg cursor-pointer"
                                    />
                                    <Input
                                        value={config.borderColor}
                                        onChange={(e) => updateConfig({ borderColor: e.target.value })}
                                        placeholder="#e2e8f0"
                                        className="flex-1 font-mono text-sm"
                                    />
                                </div>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>

                {/* Shadow Effects Section */}
                <AccordionItem
                    value="shadows"
                    className="bg-white dark:bg-slate-900 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 overflow-hidden"
                >
                    <AccordionTrigger className="px-6 py-4 text-lg font-semibold hover:no-underline hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors">
                        Shadow Effects
                    </AccordionTrigger>
                    <AccordionContent className="px-6 pb-6">
                        <div className="space-y-6">
                            <p className="text-sm text-slate-600 dark:text-slate-400 leading-relaxed">
                                Add depth and visual interest with shadow effects
                            </p>

                            <div className="space-y-3">
                                <Label htmlFor="shadow-size" className="text-sm font-medium text-slate-700 dark:text-slate-300">
                                    Shadow Size
                                </Label>
                                <Select value={config.shadowSize} onValueChange={(value) => updateConfig({ shadowSize: value })}>
                                    <SelectTrigger className="h-10">
                                        <SelectValue placeholder="Select shadow size" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="none">None</SelectItem>
                                        <SelectItem value="small">Small</SelectItem>
                                        <SelectItem value="medium">Medium</SelectItem>
                                        <SelectItem value="large">Large</SelectItem>
                                        <SelectItem value="xl">Extra Large</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </AccordionContent>
                </AccordionItem>
            </Accordion>
        </div>
    )
} 