<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;

class StoreWidgetRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        if ($this->has('name') && !$this->has('slug')) {
            $this->merge([
                'slug' => Str::slug($this->name) . '-' . Str::random(6),
            ]);
        }
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:100',
            'slug' => 'required|string|max:120|unique:widgets,slug',

            // Appearance
            'primary_color' => 'sometimes|string|max:20',
            'secondary_color' => 'sometimes|string|max:20',
            'background_color' => 'sometimes|string|max:20',
            'text_color' => 'sometimes|string|max:20',
            'border_color' => 'sometimes|string|max:20',
            'font_family' => 'sometimes|string|max:50',
            'font_size' => 'sometimes|string|in:small,medium,large',
            'border_radius' => 'sometimes|string|in:none,small,medium,large,full',
            'border_width' => 'sometimes|string|max:10',
            'shadow_size' => 'sometimes|string|in:none,small,medium,large',

            // Behavior
            'position' => 'sometimes|string|in:bottom-right,bottom-left,top-right,top-left',
            'widget_size' => 'sometimes|string|in:small,medium,large',
            'margin_x' => 'sometimes|integer|min:0|max:100',
            'margin_y' => 'sometimes|integer|min:0|max:100',
            'auto_open' => 'sometimes|boolean',
            'show_on_mobile' => 'sometimes|boolean',
            'allow_minimize' => 'sometimes|boolean',
            'auto_open_delay' => 'sometimes|integer|min:0|max:60',
            'sound_notifications' => 'sometimes|boolean',
            'typing_indicator' => 'sometimes|boolean',
            'message_timestamps' => 'sometimes|boolean',
            'response_delay' => 'sometimes|integer|min:0|max:10',
            'z_index' => 'sometimes|integer|min:1|max:99999',
            'animation_style' => 'sometimes|string|in:none,fade,slide,bounce',

            // Content
            'welcome_title' => 'sometimes|string|max:100',
            'welcome_message' => 'sometimes|string|max:1000',
            'welcome_delay' => 'sometimes|integer|min:0|max:10',
            'input_placeholder' => 'sometimes|string|max:100',
            'max_message_length' => 'sometimes|integer|min:10|max:2000',
            'chat_button_text' => 'sometimes|string|max:50',
            'send_button_text' => 'sometimes|string|max:50',
            'close_button_text' => 'sometimes|string|max:50',
            'minimize_button_text' => 'sometimes|string|max:50',
            'quick_replies' => 'sometimes|array',
            'quick_replies.*' => 'string|max:100',
            'connection_error' => 'sometimes|string|max:200',
            'typing_message' => 'sometimes|string|max:100',
            'offline_message' => 'sometimes|string|max:500',
            'company_name' => 'sometimes|string|max:100',
            'company_logo' => 'sometimes|string|nullable',
            'agent_name' => 'sometimes|string|max:100',
            'agent_avatar' => 'sometimes|string|nullable',
            'show_powered_by' => 'sometimes|boolean',
            'powered_by_text' => 'sometimes|string|max:100',
            'logo_url' => 'sometimes|string|nullable|url|max:255',
            'privacy_policy_url' => 'sometimes|string|nullable|url|max:255',
            'terms_of_service_url' => 'sometimes|string|nullable|url|max:255',
            'gdpr_compliance' => 'sometimes|boolean',

            // Tracking
            'is_active' => 'sometimes|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'A widget name is required',
            'slug.unique' => 'This widget slug is already in use',
            'primary_color.regex' => 'Please provide a valid color code',
            'quick_replies.*.max' => 'Quick reply messages must be less than 100 characters',
            'max_message_length.max' => 'Maximum message length cannot exceed 2000 characters',
        ];
    }
}
